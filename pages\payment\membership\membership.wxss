.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 会员权益 */
.benefits-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.benefits-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.benefit-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
}

.benefit-text {
  font-size: 26rpx;
  color: #666;
}

/* 套餐选择 */
.plans-section {
  margin-bottom: 40rpx;
}

.plans-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.plans-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.plan-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  position: relative;
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.plan-item.selected {
  border-color: #4B8BF5;
  transform: scale(1.02);
}

.plan-item.recommended {
  border-color: #FFD700;
  box-shadow: 0 8rpx 30rpx rgba(255, 215, 0, 0.2);
}

.recommended-badge {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #333;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  z-index: 1;
}

.plan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.plan-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.plan-discount {
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
}

.plan-duration {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.plan-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 28rpx;
  color: #4B8BF5;
  font-weight: 600;
}

.price-amount {
  font-size: 48rpx;
  color: #4B8BF5;
  font-weight: 600;
  margin-left: 5rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 15rpx;
}

.plan-description {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

/* 套餐功能特性 */
.plan-features {
  margin-bottom: 10rpx;
}

.feature-item {
  margin-bottom: 8rpx;
}

.feature-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.plan-check {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 支付按钮 */
.payment-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e5e5e5;
}

.pay-btn {
  background: linear-gradient(45deg, #4B8BF5, #6BA3F7);
  color: white;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.pay-btn:disabled {
  background: #ccc;
}

.payment-tips {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.payment-tips text {
  font-size: 22rpx;
  color: #999;
  text-align: center;
}

/* 支付确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.payment-modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.modal-overlay.show .payment-modal {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.modal-content {
  padding: 30rpx;
}

.order-info {
  margin-bottom: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.info-value.price {
  color: #4B8BF5;
  font-weight: 600;
  font-size: 32rpx;
}

.confirm-pay-btn {
  background: #4B8BF5;
  color: white;
  border: none;
  border-radius: 12rpx;
  height: 80rpx;
  font-size: 30rpx;
  font-weight: 600;
  width: 100%;
}