/**
 * 多商户功能测试文件
 * 用于验证source参数是否正确添加到API请求中
 */

// 模拟微信小程序环境
global.wx = {
  getAccountInfoSync: () => ({
    miniProgram: {
      envVersion: 'develop' // 模拟开发环境
    }
  }),
  getStorageSync: () => null,
  setStorageSync: () => {},
  removeStorageSync: () => {},
  request: (config) => {
    console.log('模拟wx.request调用:');
    console.log('URL:', config.url);
    console.log('Method:', config.method);
    console.log('Data:', config.data);
    console.log('Header:', config.header);
    
    // 检查URL是否包含source参数
    if (config.url.includes('source=')) {
      console.log('✅ URL包含source参数');
    } else {
      console.log('❌ URL缺少source参数');
    }
    
    // 模拟成功响应
    setTimeout(() => {
      config.success && config.success({
        statusCode: 200,
        data: { message: '测试成功', data: {} }
      });
    }, 100);
  },
  login: (options) => {
    setTimeout(() => {
      options.success && options.success({ code: 'test_code_123' });
    }, 100);
  },
  showToast: (options) => {
    console.log('Toast:', options.title);
  },
  hideLoading: () => {},
  showLoading: () => {}
};

// 模拟getApp函数
global.getApp = () => ({
  globalData: {
    userToken: null,
    userId: null,
    hasUserInfo: false
  }
});

// 引入配置和API模块
const apiConfig = require('../config/apiConfig');
const request = require('../utils/api/request');
const userApi = require('../utils/api/userApi');
const resumeApi = require('../utils/api/resumeApi');
const templateApi = require('../utils/api/templateApi');
const feedbackApi = require('../utils/api/feedbackApi');

/**
 * 测试商户配置
 */
function testMerchantConfig() {
  console.log('\n=== 测试商户配置 ===');
  
  // 测试获取商户代码
  const merchantCode = apiConfig.getMerchantCode();
  console.log('当前商户代码:', merchantCode);
  
  // 测试商户代码验证
  const validCodes = ['default', 'company_a', 'test-123', 'merchant_001'];
  const invalidCodes = ['', 'a', 'very_long_merchant_code_that_exceeds_limit', 'invalid@code'];
  
  console.log('\n有效商户代码测试:');
  validCodes.forEach(code => {
    const isValid = apiConfig.validateMerchantCode(code);
    console.log(`${code}: ${isValid ? '✅' : '❌'}`);
  });
  
  console.log('\n无效商户代码测试:');
  invalidCodes.forEach(code => {
    const isValid = apiConfig.validateMerchantCode(code);
    console.log(`${code}: ${isValid ? '❌ 应该无效但验证通过' : '✅'}`);
  });
  
  // 测试设置商户代码
  console.log('\n测试设置商户代码:');
  const setResult = apiConfig.setMerchantCode('test_merchant');
  console.log('设置结果:', setResult ? '✅' : '❌');
  console.log('新的商户代码:', apiConfig.getMerchantCode());
}

/**
 * 测试用户API
 */
async function testUserApi() {
  console.log('\n=== 测试用户API ===');
  
  try {
    // 测试登录API
    console.log('\n测试登录API:');
    await userApi.login('test_code_123', {
      nickName: '测试用户',
      avatarUrl: 'https://example.com/avatar.jpg'
    });
    
    // 测试获取用户信息API
    console.log('\n测试获取用户信息API:');
    await userApi.getUserInfo();
    
    // 测试刷新token API
    console.log('\n测试刷新token API:');
    await userApi.refreshToken();
    
  } catch (error) {
    console.log('API调用错误（预期的）:', error.message);
  }
}

/**
 * 测试简历API
 */
async function testResumeApi() {
  console.log('\n=== 测试简历API ===');
  
  try {
    const mockResumeData = {
      basicInfo: { name: '测试用户' },
      education: [],
      work: []
    };
    
    const mockThemeConfig = {
      themeColor: '#2E75B6',
      fontSize: 12
    };
    
    // 测试生成预览图片
    console.log('\n测试生成预览图片API:');
    await resumeApi.generatePreviewImage(mockResumeData, mockThemeConfig, 'templateA02');
    
    // 测试生成PDF
    console.log('\n测试生成PDF API:');
    await resumeApi.generatePDF(mockResumeData, mockThemeConfig, 'templateA02');
    
  } catch (error) {
    console.log('API调用错误（预期的）:', error.message);
  }
}

/**
 * 测试模板API
 */
async function testTemplateApi() {
  console.log('\n=== 测试模板API ===');
  
  try {
    // 测试获取模板列表
    console.log('\n测试获取模板列表API:');
    await templateApi.getTemplateList({
      page: 1,
      pageSize: 10,
      type: 'word'
    });
    
    // 测试搜索模板
    console.log('\n测试搜索模板API:');
    await templateApi.searchTemplates({
      keyword: '商务',
      type: 'word'
    });
    
  } catch (error) {
    console.log('API调用错误（预期的）:', error.message);
  }
}

/**
 * 测试反馈API
 */
async function testFeedbackApi() {
  console.log('\n=== 测试反馈API ===');
  
  try {
    // 测试提交反馈
    console.log('\n测试提交反馈API:');
    await feedbackApi.submitFeedback({
      type: 'bug',
      content: '测试反馈内容',
      contact: '<EMAIL>',
      deviceInfo: { platform: 'test' }
    });
    
  } catch (error) {
    console.log('API调用错误（预期的）:', error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始多商户功能测试...\n');
  
  // 测试商户配置
  testMerchantConfig();
  
  // 等待一下，让日志更清晰
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 测试各个API模块
  await testUserApi();
  await testResumeApi();
  await testTemplateApi();
  await testFeedbackApi();
  
  console.log('\n=== 测试完成 ===');
  console.log('请检查上面的日志，确保所有API请求都包含source参数');
}

// 运行测试
runAllTests().catch(console.error);
