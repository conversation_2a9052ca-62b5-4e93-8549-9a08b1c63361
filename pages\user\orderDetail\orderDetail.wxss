.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
}

/* 订单状态 */
.status-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 24rpx;
  color: #FF9800;
}

/* 通用section样式 */
.product-section,
.order-section,
.amount-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 25rpx;
}

/* 商品信息 */
.product-item {
  display: flex;
  align-items: center;
}

.product-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.product-duration {
  font-size: 24rpx;
  color: #666;
}

.product-price {
  font-size: 30rpx;
  font-weight: 600;
  color: #4B8BF5;
}

/* 订单详情 */
.order-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 金额明细 */
.amount-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
}

.amount-row.total {
  border-top: 1rpx solid #e5e5e5;
  padding-top: 20rpx;
  margin-top: 10rpx;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
}

.amount-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.amount-row.total .amount-label,
.amount-row.total .amount-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #4B8BF5;
}

/* 操作按钮 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  gap: 15rpx;
}

.pay-btn {
  flex: 2;
  background: #4B8BF5;
  color: white;
  border: none;
  border-radius: 25rpx;
  height: 70rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.cancel-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 25rpx;
  height: 70rpx;
  font-size: 28rpx;
}

.contact-btn {
  flex: 1;
  background: white;
  color: #4B8BF5;
  border: 2rpx solid #4B8BF5;
  border-radius: 25rpx;
  height: 70rpx;
  font-size: 28rpx;
}