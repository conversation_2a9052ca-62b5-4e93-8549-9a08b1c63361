<view class="container">
  <view class="feedback-form">
    <view class="form-section">
      <text class="section-title">反馈类型</text>
      <view class="feedback-types">
        <view 
          class="type-item {{selectedType === item.value ? 'selected' : ''}}"
          wx:for="{{feedbackTypes}}"
          wx:key="value"
          bindtap="selectType"
          data-type="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="form-section">
      <text class="section-title">问题描述</text>
      <textarea 
        class="feedback-textarea"
        placeholder="请详细描述您遇到的问题或建议..."
        value="{{feedbackContent}}"
        bindinput="onContentInput"
        maxlength="500"
        show-confirm-bar="{{false}}"
      ></textarea>
      <text class="char-count">{{feedbackContent.length}}/500</text>
    </view>

    <view class="form-section">
      <text class="section-title">联系方式（选填）</text>
      <input 
        class="contact-input"
        placeholder="请输入您的微信号或手机号"
        value="{{contactInfo}}"
        bindinput="onContactInput"
      />
    </view>

    <button class="submit-btn" bindtap="submitFeedback" disabled="{{!canSubmit}}">
      提交反馈
    </button>
  </view>

  <view class="contact-info">
    <text class="contact-title">其他联系方式</text>
    <view class="contact-item">
      <text class="contact-label">客服微信：</text>
      <text class="contact-value" bindtap="copyWechat">km80048</text>
    </view>
  </view>
</view>