Page({
  data: {
    feedbackTypes: [
      { label: '功能建议', value: 'suggestion' },
      { label: '问题反馈', value: 'bug' },
      { label: '使用咨询', value: 'question' },
      { label: '其他', value: 'other' }
    ],
    selectedType: '',
    feedbackContent: '',
    contactInfo: '',
    canSubmit: false
  },

  // 选择反馈类型
  selectType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 输入反馈内容
  onContentInput(e) {
    this.setData({
      feedbackContent: e.detail.value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 输入联系方式
  onContactInput(e) {
    this.setData({
      contactInfo: e.detail.value
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { selectedType, feedbackContent } = this.data;
    const canSubmit = selectedType && feedbackContent.trim().length >= 10;
    this.setData({ canSubmit });
  },

  // 提交反馈
  async submitFeedback() {
    const { selectedType, feedbackContent, contactInfo } = this.data;

    if (!selectedType || !feedbackContent.trim()) {
      wx.showToast({
        title: '请完善反馈信息',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '提交中...' });

    try {
      // 这里应该调用反馈API
      const feedbackData = {
        type: selectedType,
        content: feedbackContent.trim(),
        contact: contactInfo.trim(),
        timestamp: Date.now()
      };

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      // 重置表单
      this.setData({
        selectedType: '',
        feedbackContent: '',
        contactInfo: '',
        canSubmit: false
      });

    } catch (error) {
      wx.hideLoading();
      console.error('提交反馈失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  // 复制微信号
  copyWechat() {
    wx.setClipboardData({
      data: 'km80048',
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success'
        });
      }
    });
  }
});