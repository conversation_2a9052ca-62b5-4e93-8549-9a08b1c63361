<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">卡密兑换</text>
    <text class="subtitle">输入卡密获取专属权益</text>
  </view>

  <!-- 卡密输入区域 -->
  <view class="input-section">
    <view class="input-group">
      <view class="input-label">卡密码</view>
      <view class="input-wrapper">
        <input 
          class="card-input" 
          placeholder="请输入卡密码"
          value="{{cardCode}}"
          bindinput="onCardCodeInput"
          maxlength="50"
        />
        <button class="clear-btn" bindtap="clearInput" wx:if="{{cardCode}}">
          <text class="clear-icon">×</text>
        </button>
      </view>
    </view>

    <!-- 验证结果 -->
    <view class="validation-result" wx:if="{{validationResult}}">
      <view class="result-item {{validationResult.valid ? 'valid' : 'invalid'}}">
        <text class="result-icon">{{validationResult.valid ? '✓' : '✗'}}</text>
        <view class="result-content">
          <text class="result-text" wx:if="{{validationResult.valid}}">
            {{validationResult.benefit_description}}
          </text>
          <text class="result-text" wx:else>
            {{validationResult.message}}
          </text>
          <text class="result-detail" wx:if="{{validationResult.valid && validationResult.expires_at}}">
            有效期至：{{validationResult.expires_at}}
          </text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button 
        class="validate-btn" 
        bindtap="validateCard"
        loading="{{validating}}"
        disabled="{{!cardCode || validating}}"
      >
        {{validating ? '验证中...' : '验证卡密'}}
      </button>
      
      <button 
        class="redeem-btn" 
        bindtap="redeemCard"
        loading="{{redeeming}}"
        disabled="{{!validationResult || !validationResult.valid || redeeming}}"
      >
        {{redeeming ? '兑换中...' : '立即兑换'}}
      </button>
    </view>

    <!-- 测试按钮（开发环境） -->
    <view class="test-section">
      <button class="test-btn" bindtap="copyTestCard">
        复制测试卡密
      </button>
    </view>
  </view>

  <!-- 当前权益 -->
  <view class="benefits-section" wx:if="{{benefits}}">
    <view class="section-title">当前权益</view>
    
    <!-- 次数权益 -->
    <view class="benefit-card" wx:if="{{benefits.total_quota > 0}}">
      <view class="benefit-header">
        <text class="benefit-title">使用次数</text>
        <text class="benefit-value">{{benefits.total_quota}} 次</text>
      </view>
      <view class="quota-list" wx:if="{{benefits.quota_benefits && benefits.quota_benefits.length > 0}}">
        <view class="quota-item" wx:for="{{benefits.quota_benefits}}" wx:key="id">
          <text class="quota-text">剩余 {{item.remaining_quota}} 次</text>
          <text class="quota-expire" wx:if="{{item.expires_at}}">
            {{item.expires_at}} 到期
          </text>
        </view>
      </view>
    </view>

    <!-- 会员权益 -->
    <view class="benefit-card" wx:if="{{benefits.has_active_membership}}">
      <view class="benefit-header">
        <text class="benefit-title">会员权益</text>
        <text class="benefit-status active">有效</text>
      </view>
      <text class="membership-expire" wx:if="{{benefits.membership_expires_at}}">
        到期时间：{{benefits.membership_expires_at}}
      </text>
    </view>

    <!-- 无权益提示 -->
    <view class="no-benefits" wx:if="{{benefits.total_quota === 0 && !benefits.has_active_membership}}">
      <text class="no-benefits-text">暂无可用权益</text>
      <text class="no-benefits-tip">兑换卡密获取专属权益</text>
    </view>
  </view>

  <!-- 兑换历史 -->
  <view class="history-section" wx:if="{{redeemHistory && redeemHistory.length > 0}}">
    <view class="section-title">兑换历史</view>
    <view class="history-list">
      <view class="history-item" wx:for="{{redeemHistory}}" wx:key="id">
        <view class="history-content">
          <text class="history-benefit">{{item.benefit_text}}</text>
          <text class="history-time">{{item.created_at}}</text>
        </view>
        <view class="history-status">
          <text class="status-text success">已兑换</text>
        </view>
      </view>
    </view>
  </view>
</view>
