.container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50rpx 30rpx;
}

.result-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 50rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600rpx;
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 15rpx;
}

.success-result .result-title {
  color: #4CAF50;
}

.failed-result .result-title {
  color: #F44336;
}

.cancelled-result .result-title {
  color: #FF9800;
}

.result-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
  display: block;
}

/* 订单信息 */
.order-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.primary-btn {
  background: linear-gradient(45deg, #4B8BF5, #6BA3F7);
  color: white;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.secondary-btn {
  background: white;
  color: #4B8BF5;
  border: 2rpx solid #4B8BF5;
  border-radius: 50rpx;
  height: 80rpx;
  font-size: 30rpx;
  font-weight: 600;
}