const cardApi = require('../../../utils/api/cardApi.js');
const membershipManager = require('../../../utils/user/membershipManager.js');

Page({
  data: {
    cardCode: '',
    validating: false,
    redeeming: false,
    validationResult: null,
    benefits: [],
    redeemHistory: []
  },

  onLoad() {
    this.loadUserBenefits();
    this.loadRedeemHistory();
  },

  onShow() {
    // 页面显示时刷新权益信息
    this.loadUserBenefits();
  },

  // 输入卡密
  onCardCodeInput(e) {
    this.setData({
      cardCode: e.detail.value.trim(),
      validationResult: null
    });
  },

  // 验证卡密
  async validateCard() {
    const { cardCode } = this.data;
    
    if (!cardCode) {
      wx.showToast({
        title: '请输入卡密',
        icon: 'none'
      });
      return;
    }

    this.setData({ validating: true });

    try {
      const result = await cardApi.validateCard(cardCode);
      
      if (result.valid) {
        this.setData({
          validationResult: {
            valid: true,
            benefit_type: result.benefit_type,
            benefit_value: result.benefit_value,
            benefit_unit: result.benefit_unit,
            benefit_description: result.benefit_description,
            expires_at: result.expires_at
          }
        });
        
        wx.showToast({
          title: '卡密验证成功',
          icon: 'success'
        });
      } else {
        this.setData({
          validationResult: {
            valid: false,
            message: result.message || '卡密无效'
          }
        });
        
        wx.showToast({
          title: result.message || '卡密无效',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('验证卡密失败:', error);
      wx.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      });
    }

    this.setData({ validating: false });
  },

  // 兑换卡密
  async redeemCard() {
    const { cardCode, validationResult } = this.data;
    
    if (!cardCode) {
      wx.showToast({
        title: '请输入卡密',
        icon: 'none'
      });
      return;
    }

    if (!validationResult || !validationResult.valid) {
      wx.showToast({
        title: '请先验证卡密',
        icon: 'none'
      });
      return;
    }

    this.setData({ redeeming: true });

    try {
      const result = await cardApi.redeemCard(cardCode);
      
      if (result.success !== false) {
        wx.showToast({
          title: '兑换成功',
          icon: 'success'
        });

        // 清空输入
        this.setData({
          cardCode: '',
          validationResult: null
        });

        // 刷新权益信息
        this.loadUserBenefits();
        this.loadRedeemHistory();

        // 如果是会员权益，刷新会员状态
        if (result.benefit_type === 'membership') {
          membershipManager.queryMemberStatus(true);
        }

        // 显示兑换结果
        this.showRedeemResult(result);
      } else {
        throw new Error(result.message || '兑换失败');
      }
    } catch (error) {
      console.error('兑换卡密失败:', error);
      wx.showToast({
        title: error.message || '兑换失败',
        icon: 'none'
      });
    }

    this.setData({ redeeming: false });
  },

  // 显示兑换结果
  showRedeemResult(result) {
    const benefitText = this.formatBenefitText(
      result.benefit_type,
      result.benefit_value,
      result.benefit_unit
    );

    wx.showModal({
      title: '兑换成功',
      content: `恭喜您获得：${benefitText}`,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 格式化权益文本
  formatBenefitText(type, value, unit) {
    if (type === 'membership') {
      if (unit === 'days') {
        return `会员权益 ${value} 天`;
      } else if (unit === 'months') {
        return `会员权益 ${value} 个月`;
      }
    } else if (type === 'quota') {
      return `使用次数 ${value} 次`;
    }
    return `${type} ${value} ${unit}`;
  },

  // 加载用户权益
  async loadUserBenefits() {
    try {
      const result = await cardApi.getCardBenefits();
      
      if (result.success !== false) {
        this.setData({
          benefits: {
            quota_benefits: result.quota_benefits || [],
            total_quota: result.total_quota || 0,
            membership_benefits: result.membership_benefits || [],
            has_active_membership: result.has_active_membership || false,
            membership_expires_at: result.membership_expires_at
          }
        });
      }
    } catch (error) {
      console.error('加载用户权益失败:', error);
    }
  },

  // 加载兑换历史
  async loadRedeemHistory() {
    try {
      const result = await cardApi.getCardHistory({
        page: 1,
        page_size: 10
      });
      
      if (result.success !== false && result.records) {
        this.setData({
          redeemHistory: result.records.map(record => ({
            ...record,
            created_at: this.formatTime(record.created_at),
            benefit_text: this.formatBenefitText(
              record.benefit_type,
              record.benefit_value,
              record.benefit_unit
            )
          }))
        });
      }
    } catch (error) {
      console.error('加载兑换历史失败:', error);
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 清空输入
  clearInput() {
    this.setData({
      cardCode: '',
      validationResult: null
    });
  },

  // 复制卡密（用于测试）
  copyTestCard() {
    wx.setClipboardData({
      data: 'TEST-CARD-2024',
      success: () => {
        wx.showToast({
          title: '测试卡密已复制',
          icon: 'success'
        });
      }
    });
  }
});
