# 微信小程序后端API接口文档

**版本:** 2.3.0
**最后更新:** 2025-07-19

## 1. 基础信息

- **服务地址 (示例):** `https://your-domain.com`
- **认证方式:** 所有需要认证的接口, 必须在请求头中携带 `Authorization: Bearer <access_token>`。
- **商户与来源标识:** 部分接口需要通过请求头或查询参数传递来源标识, 以确定商户上下文。
  - **请求头 (推荐):** `X-App-Source: <source_code>`
  - **查询参数 (备用):** `?source=<source_code>`

---

## 2. 核心流程：权益消耗

**重要:** 权益的消耗不再由服务端业务接口 (如`/resume/export-pdf`) 自动记录, 而是改为**前端在用户完成操作后, 调用 `/user/action/report` 接口主动上报**。

1.  **(可选) 预检查:** 调用 `POST /user/action/validate-permission` 检查用户是否有权限。
2.  **执行操作:** 调用具体的业务接口 (如 `POST /resume/export-pdf`)。
3.  **上报消耗:** 用户在前端**真正完成**操作后 (如文件下载成功), 调用 `POST /user/action/report` 上报。

---

## 3. 用户认证模块 (`/auth`)

### 3.1 微信登录

- **Endpoint:** `POST /auth/login`
- **描述:** 使用微信code进行登录或注册, 返回access token。
- **认证:** 不需要
- **请求体 (`application/json`):**
  ```json
  {
    "code": "string (required) - 微信登录wx.login()获取的code",
    "user_info": {
      "nickName": "string (optional) - 用户昵称",
      "avatarUrl": "string (optional) - 头像URL",
      "gender": "integer (optional) - 性别: 0未知, 1男, 2女",
      "country": "string (optional)",
      "province": "string (optional)",
      "city": "string (optional)"
    }
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "access_token": "string - JWT访问令牌",
    "token_type": "string - 固定为 'bearer'",
    "expires_in": "integer - 令牌过期时间(秒)",
    "user_info": {
      "id": "integer",
      "openid": "string",
      "nickname": "string",
      "avatar_url": "string",
      "gender": "integer",
      "country": "string",
      "province": "string",
      "city": "string",
      "is_member": "boolean",
      "created_at": "string (datetime)",
      "updated_at": "string (datetime)"
    }
  }
  ```

### 3.2 获取当前用户信息

- **Endpoint:** `GET /auth/user`
- **描述:** 获取当前登录���户的详细信息。
- **认证:** 需要
- **成功响应 (200 OK):** (结构同登录响应中的 `user_info` 对象)

### 3.3 更新用户信息
- **Endpoint:** `PUT /auth/user`
- **描述:** 更新当前用户的基本信息。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "nickname": "string (optional)",
    "avatar_url": "string (optional)",
    "gender": "integer (optional)",
    "country": "string (optional)",
    "province": "string (optional)",
    "city": "string (optional)"
  }
  ```
- **成功响应 (200 OK):** (结构同登录响应中的 `user_info` 对象)

### 3.4 刷新访问令牌

- **Endpoint:** `POST /auth/refresh`
- **描述:** 使用有效的旧token换取新的access token。
- **认证:** 需要
- **成功响应 (200 OK):** (结构同 `POST /auth/login` 的响应)

### 3.5 查询会员状态 (简版)
- **Endpoint:** `GET /auth/member-status`
- **描述:** 快速查询当前用户的会员状态。
- **认证:** 需要
- **成功响应 (200 OK):**
  ```json
  {
    "is_member": "boolean",
    "openid": "string",
    "message": "string"
  }
  ```

---

## 4. 用户操作与权益模块 (`/user/action`)

### 4.1 验证操作权限 (预检查)

- **Endpoint:** `POST /user/action/validate-permission`
- **描述:** 在��行操作前, 检查用户是否有足够权限 (会员状态、剩余次数等)。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "action_type": "string (required) - 操作类型, 如 'download_pdf'",
    "check_quota": "boolean (optional, default=true) - 是否检查配额"
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "success": "boolean",
    "data": {
      "has_permission": "boolean - 是否有权限",
      "is_member_action": "boolean - 是否为会员专属操作",
      "feature": "string - 功能名称",
      "reason": "string - 权限描述, 如 '今日还可使用5次'",
      "usage_info": {
        "limit": "integer - 限制次数 (-1为无限)",
        "used": "integer - 已用次数",
        "remaining": "integer - 剩余次数"
      },
      "is_member": "boolean - 当前是否为会员"
    },
    "message": "string"
  }
  ```

### 4.2 上报用户操作 (权益消耗)

- **Endpoint:** `POST /user/action/report`
- **描述:** 用户完成操作后, 前端调用此接口记录, 并消耗权益。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "action_type": "string (required) - 操作类型",
    "action_content": "object (optional) - 操作相关内容",
    "feature_name": "string (optional) - 关联的功能名称",
    "resource_type": "string (optional) - 资源类型, 如 'pdf'",
    "resource_id": "string (optional) - 资源ID",
    "file_size": "integer (optional) - 文件大小(字节)",
    "file_format": "string (optional) - 文件格式",
    "operation_status": "string (optional, default='completed')",
    "error_message": "string (optional) - 如果操作失败, 附带错误信息",
    "consumed_quota": "integer (optional, default=1) - 消耗的配额",
    "client_info": "object (optional) - 客户端信息",
    "ip_address": "string (optional) - 用户IP",
    "template_id": "string (optional) - 模板ID"
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "success": "boolean",
    "message": "string",
    "action_id": "integer - 创建的操作记录ID",
    "is_member_action": "boolean",
    "consumed_quota": "integer"
  }
  ```

### 4.3 获取用户操作统计

- **Endpoint:** `GET /user/action/stats`
- **描述:** 获取当前用户的操作统计信息。
- **认证:** 需要
- **查询参数:**
  - `days` (integer, optional, default=30): 统计最近N天的数据。
- **成功响应 (200 OK):**
  ```json
  {
    "success": "boolean",
    "data": {
      "period_days": "integer",
      "start_date": "string (date)",
      "end_date": "string (date)",
      "total_actions": "integer",
      "member_actions": "integer",
      "feature_stats": {
        "resume_export": { "count": "integer", "total_quota": "integer" }
      },
      "status_stats": { "completed": "integer", "failed": "integer" }
    },
    "message": "string"
  }
  ```

---

## 5. 简历模块 (`/resume`)

### 5.1 导出PDF / 导出JPEG / 预览简历

- **Endpoints:**
  - `POST /resume/export-pdf`
  - `POST /resume/export-jpeg`
  - `POST /resume/preview`
- **描述:**
  - `export-pdf`: 将简历数据导出为PDF文件流。
  - `export-jpeg`: 将简历数据导出为JPEG图片, 返回临时文件URL。
  - `preview`: 预览简历渲染结果, 返回HTML内容用于调试。
- **认证:** `export-pdf`和`export-jpeg`需要认证, `preview`可选。
- **请求体 (`application/json`):**
  ```json
  {
    "resume_data": {
      "moduleOrders": {
        "education": "integer",
        "work": "integer",
        "project": "integer",
        "skills": "integer"
      },
      "basicInfo": {
        "name": "string",
        "gender": "string",
        "phone": "string",
        "photoUrl": "string (base64)",
        "city": "string",
        "email": "string",
        "wechat": "string",
        "age": "string",
        "birthday": "string",
        "marriage": "string",
        "politics": "string",
        "nation": "string",
        "hometown": "string",
        "height": "string",
        "weight": "string",
        "educationLevel": "string"
      },
      "jobIntention": {
        "position": "string",
        "city": "string",
        "salary": "string",
        "status": "string"
      },
      "education": [
        {
          "school": "string",
          "major": "string",
          "degree": "string",
          "startDate": "string",
          "endDate": "string",
          "description": "string"
        }
      ],
      "work": [
        {
          "company": "string",
          "position": "string",
          "startDate": "string",
          "endDate": "string",
          "description": "string"
        }
      ],
      "project": [
        {
          "projectName": "string",
          "role": "string",
          "startDate": "string",
          "endDate": "string",
          "description": "string"
        }
      ],
      "skills": ["string"],
      "awards": ["string"],
      "interests": ["string"],
      "evaluation": [{"content": "string", "moduleOrder": "integer"}]
    },
    "template_id": "string (required)",
    "theme_config": {
      "themeColor": "string (hex color)",
      "fontSize": "integer",
      "spacing": "number"
    }
  }
  ```

### 5.2 获取模板列表

- **Endpoint:** `GET /resume/templates`
- **描述:** 获取所有可用的简历模板列表。
- **认证:** 不需要
- **成功响应 (200 OK):**
  ```json
  {
    "message": "string",
    "templates": ["string"]
  }
  ```

---

## 6. 支付与会员模块

### 6.1 获取会员套餐列表

- **Endpoint:** `GET /payment/plans`
- **描述:** 获取所有可购买的会员套餐。
- **认证:** 不需要
- **成功响应 (200 OK):**
  ```json
  {
    "plans": [
      {
        "id": "integer",
        "name": "string",
        "description": "string",
        "price": "number - 价格(分)",
        "original_price": "number - 原价(分)",
        "duration_days": "integer",
        "features": ["string"],
        "is_active": "boolean",
        "is_recommended": "boolean",
        "sort_order": "integer",
        "created_at": "string (datetime)",
        "updated_at": "string (datetime)",
        "discount_rate": "number - 折扣率",
        "price_yuan": "number - 价格(元)",
        "original_price_yuan": "number - 原价(元)"
      }
    ],
    "total": "integer"
  }
  ```

### 6.2 创建订单

- **Endpoint:** `POST /payment/create-order`
- **描述:** 为购买会员套餐创建一个待支付订单。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "plan_id": "integer (required)",
    "client_ip": "string (optional)",
    "user_agent": "string (optional)"
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "id": "string - 订单号",
    "user_id": "integer",
    "plan_id": "integer",
    "amount": "number - 订单金额(分)",
    "original_amount": "number",
    "discount_amount": "number",
    "status": "string (enum: 'pending', 'paid', 'cancelled', 'expired', 'refunded')",
    "wx_prepay_id": "string",
    "wx_transaction_id": "string",
    "paid_at": "string (datetime)",
    "expired_at": "string (datetime)",
    "created_at": "string (datetime)",
    "updated_at": "string (datetime)",
    "plan_name": "string",
    "amount_yuan": "number - 订单金额(元)"
  }
  ```

### 6.3 创建微信支付参数

- **Endpoint:** `POST /payment/wechat-pay`
- **描述:** 为指定订单创建微信支付所��的参数。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "order_id": "string (required) - 订单号"
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "appId": "string",
    "timeStamp": "string",
    "nonceStr": "string",
    "package": "string",
    "signType": "string",
    "paySign": "string",
    "order_id": "string"
  }
  ```

### 6.4 获取当前会员状态 (详细)

- **Endpoint:** `GET /membership/status`
- **描述:** 获取当前用户的会员状态、权益和历史记录。
- **认证:** 需要
- **成功响应 (200 OK):**
  ```json
  {
    "is_member": "boolean",
    "current_membership": {
      "id": "integer",
      "user_id": "integer",
      "plan_id": "integer",
      "plan_name": "string",
      "start_date": "string (datetime)",
      "end_date": "string (datetime)",
      "is_active": "boolean",
      "auto_renew": "boolean",
      "activated_at": "string (datetime)",
      "remaining_days": "integer"
    },
    "membership_history": [
      {
        "... (同 current_membership 结构)"
      }
    ]
  }
  ```

---

## 7. 卡密兑换模块 (`/cards`)

### 7.1 兑换卡密

- **Endpoint:** `POST /cards/redeem`
- **描述:** 用户提交卡密进行兑��。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "card_code": "string (required) - 卡密码"
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "success": "boolean",
    "message": "string",
    "benefit_type": "string (enum: 'quota', 'membership')",
    "benefit_value": "integer",
    "benefit_unit": "string (enum: 'times', 'days', 'months')",
    "benefit_description": "string"
  }
  ```

### 7.2 验证卡密有效性

- **Endpoint:** `GET /cards/validate/{card_code}`
- **描述:** 检查卡密的有效性及其包含的权益。
- **认证:** 需要
- **路径参数:**
  - `card_code` (string, required): 要验证的卡密码。
- **成功响应 (200 OK):**
  ```json
  {
    "valid": "boolean",
    "message": "string",
    "benefit_type": "string (enum)",
    "benefit_value": "integer",
    "benefit_unit": "string (enum)",
    "benefit_description": "string",
    "expires_at": "string (datetime) | null"
  }
  ```

### 7.3 获取用户权益汇总

- **Endpoint:** `GET /cards/benefits`
- **描述:** 获取用户通过卡密兑换的所有有效权益。
- **认证:** 需要
- **成功响应 (200 OK):**
  ```json
  {
    "quota_benefits": [
      {
        "id": "integer",
        "benefit_type": "string (enum: 'quota')",
        "remaining_quota": "integer",
        "expires_at": "string (datetime) | null",
        "source": "string",
        "source_id": "integer",
        "is_active": "boolean",
        "created_at": "string (datetime)"
      }
    ],
    "total_quota": "integer - 总剩余次数",
    "membership_benefits": [
      {
        "... (同 quota_benefits 结构, 但类型为 'membership')"
      }
    ],
    "has_active_membership": "boolean",
    "membership_expires_at": "string (datetime) | null"
  }
  ```

---

## 8. 证件照模块 (`/idphoto`)

### 8.1 生成证件照 (一步)

- **Endpoint:** `POST /idphoto/generate`
- **描述:** 上传照片, 直接生成指定尺寸和背景色的证件照。
- **认证:** 需要
- **请求 (`multipart/form-data`):**
  - `image` (file, required): 用户上传的图片文件。
  - `size` (string, optional, default='one_inch'): 尺寸, 见下文枚举。
  - `color` (string, optional, default='white'): 颜色, 见下文枚举。
- **成功响应 (200 OK):**
  ```json
  {
    "success": "boolean",
    "message": "string",
    "data": {
      "image_base64": "string - base64编码的证件照图片",
      "size": "string",
      "size_name": "string",
      "color": "string",
      "color_name": "string",
      "dimensions": { "width": "integer", "height": "integer" },
      "hd_image_base64": "string",
      "transparent_base64": "string"
    }
  }
  ```
- **Size Enum:** `one_inch`, `two_inch`, `big_one_inch`, `small_one_inch`, `big_two_inch`, `small_two_inch`
- **Color Enum:** `transparent`, `white`, `blue`, `red`, `blue_gradient`, `red_gradient`

### 8.2 获取支持的尺寸和颜色

- **Endpoints:**
  - `GET /idphoto/sizes`: 获取所有支持的证件照尺寸规格。
  - `GET /idphoto/colors`: 获取所有支持的背景颜色选项。
- **认证:** 不需要

---

## 9. 其他模块

### 9.1 用户反馈 (`/feedback`)

- **Endpoint:** `POST /feedback`
- **描述:** 用户提交反馈信息。
- **认证:** 需要
- **请求体 (`application/json`):**
  ```json
  {
    "content": "string (required, min:1, max:2000)",
    "contact_info": "string (optional, max:200)"
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "message": "反馈提交成功, 我们会尽快处理"
  }
  ```

### 9.2 免费模板 (`/free-templates`)

- **Endpoint:** `GET /free-templates`
- **描述:** 获取免费简历模板列��。
- **认证:** 不需要
- **查询参数:**
  - `batch_flag` (string, optional): 按批次筛选。
  - `type` (string, optional): 按类型筛选。
  - `skip` (integer, optional, default=0): 偏移量。
  - `limit` (integer, optional, default=20): 数量限制。
- **成功响应 (200 OK):**
  ```json
  {
    "total": "integer",
    "templates": [
      {
        "id": "string",
        "thumb_url": "string",
        "baidu_url": "string",
        "baidu_pass": "string",
        "quark_url": "string",
        "quark_pass": "string",
        "type": "string"
      }
    ]
  }
  ```

### 9.3 错误上报 (`/error-report`)

- **Endpoint:** `POST /error-report`
- **描述:** 前端上报JS等客户端错误。
- **认证:** 可选
- **请求体 (`application/json`):**
  ```json
  {
    "errors": [
      {
        "error_type": "string (required)",
        "error_message": "string (required)",
        "error_context": "object (optional)"
      }
    ]
  }
  ```
- **成功响应 (200 OK):**
  ```json
  {
    "success": "boolean",
    "message": "string"
  }
  ```

### 9.4 健康检查 (`/health`)

- **Endpoints:**
  - `GET /health/ping`: 基础健康检查。
  - `GET /health/check`: 完整健康检查 (含数据库等)。
- **描述:** 用于��务监控。
- **认证:** 不需要

---
*文档生成完毕。*
