<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">开通会员</text>
    <text class="subtitle">解锁全部功能，享受专属服务</text>
  </view>

  <!-- 会员权益展示 -->
  <view class="benefits-section">
    <view class="benefits-title">会员专享权益</view>
    <view class="benefits-grid">
      <view class="benefit-item" wx:for="{{benefits}}" wx:key="index">
        <image class="benefit-icon" src="{{item.icon}}" mode="aspectFit"></image>
        <text class="benefit-text">{{item.text}}</text>
      </view>
    </view>
  </view>

  <!-- 套餐选择 -->
  <view class="plans-section">
    <view class="plans-title">选择套餐</view>
    <view class="plans-list">
      <view
        class="plan-item {{selectedPlan === item.id ? 'selected' : ''}} {{item.is_recommended ? 'recommended' : ''}}"
        wx:for="{{membershipPlans}}"
        wx:key="id"
        bindtap="selectPlan"
        data-plan="{{item}}"
      >
        <view wx:if="{{item.is_recommended}}" class="recommended-badge">推荐</view>
        <view class="plan-header">
          <view class="plan-name">{{item.name}}</view>
          <view wx:if="{{item.discount}}" class="plan-discount">{{item.discount}}</view>
        </view>
        <view class="plan-duration">{{item.duration}}</view>
        <view class="plan-price">
          <text class="price-symbol">¥</text>
          <text class="price-amount">{{item.price}}</text>
          <text wx:if="{{item.original_price}}" class="original-price">¥{{item.original_price}}</text>
        </view>
        <view class="plan-description">{{item.description}}</view>

        <!-- 显示套餐功能特性 -->
        <view wx:if="{{item.features && item.features.length > 0}}" class="plan-features">
          <view class="feature-item" wx:for="{{item.features}}" wx:for-item="feature" wx:key="*this">
            <text class="feature-text">• {{feature}}</text>
          </view>
        </view>

        <view class="plan-check">
          <image wx:if="{{selectedPlan === item.id}}" class="check-icon" src="/pages/payment/membership/images/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="payment-section">
    <button class="pay-btn" bindtap="createOrder" disabled="{{!selectedPlan}}">
      立即支付
    </button>
    <view class="payment-tips">
      <text>• 支付成功后立即生效</text>
      <text>• 支持微信支付</text>
      <text>• 如有问题请联系客服</text>
    </view>
  </view>
</view>

<!-- 支付确认弹窗 -->
<view class="modal-overlay {{showPaymentModal ? 'show' : ''}}" bindtap="hidePaymentModal">
  <view class="payment-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">确认支付</text>
      <image class="close-btn" src="/pages/payment/membership/images/close.png" bindtap="hidePaymentModal" mode="aspectFit"></image>
    </view>
    
    <view class="modal-content">
      <view class="order-info">
        <view class="info-row">
          <text class="info-label">套餐：</text>
          <text class="info-value">{{selectedPlanInfo.name}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">时长：</text>
          <text class="info-value">{{selectedPlanInfo.duration}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">金额：</text>
          <text class="info-value price">¥{{selectedPlanInfo.price}}</text>
        </view>
      </view>
      
      <button class="confirm-pay-btn" bindtap="confirmPayment" loading="{{paymentLoading}}">
        {{paymentLoading ? '处理中...' : '确认支付'}}
      </button>
    </view>
  </view>
</view>