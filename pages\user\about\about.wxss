.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
}

/* 应用信息 */
.app-info {
  background: white;
  border-radius: 20rpx;
  padding: 50rpx 30rpx;
  text-align: center;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.app-version {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 20rpx;
}

.app-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 功能介绍 */
.features-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.feature-item {
  display: flex;
  align-items: center;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 联系我们 */
.contact-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.contact-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.contact-content {
  flex: 1;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.contact-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #4B8BF5;
}

.copy-icon {
  width: 30rpx;
  height: 30rpx;
  opacity: 0.5;
}

/* 法律信息 */
.legal-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.legal-links {
  display: flex;
  gap: 40rpx;
}

.legal-link {
  font-size: 26rpx;
  color: #4B8BF5;
  text-decoration: underline;
}

/* 版权信息 */
.copyright {
  text-align: center;
  padding: 30rpx;
}

.copyright-text {
  font-size: 24rpx;
  color: #999;
  display: block;
  line-height: 1.5;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.policy-modal {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.modal-overlay.show .policy-modal {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.modal-content {
  padding: 30rpx;
  max-height: 60vh;
}

.policy-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
}