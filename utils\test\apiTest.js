/**
 * API接口测试工具
 * 用于验证所有适配后的接口功能是否与服务端API文档一致
 */

// 导入所有API模块
const userApi = require('../api/userApi');
const paymentApi = require('../api/paymentApi');
const membershipApi = require('../api/membershipApi');
const cardApi = require('../api/cardApi');
const feedbackApi = require('../api/feedbackApi');
const resumeApi = require('../api/resumeApi');
const idPhotoApiNew = require('../api/idPhotoApiNew');

/**
 * API测试类
 */
class ApiTester {

  /**
   * 测试用户认证模块接口
   */
  async testUserAuthApis() {
    console.log('=== 测试用户认证模块接口 ===');
    
    const results = {
      module: '用户认证模块',
      tests: []
    };

    try {
      // 测试获取用户信息（需要登录）
      console.log('测试获取用户信息...');
      const userInfoResult = await userApi.getUserInfo();
      results.tests.push({
        name: '获取用户信息',
        url: '/auth/user',
        method: 'GET',
        success: userInfoResult.success !== false,
        response: userInfoResult
      });

      // 测试查询会员状态（需要登录）
      console.log('测试查询会员状态...');
      const memberStatusResult = await userApi.getMemberStatus();
      results.tests.push({
        name: '查询会员状态',
        url: '/auth/member-status',
        method: 'GET',
        success: memberStatusResult.success !== false,
        response: memberStatusResult
      });

    } catch (error) {
      console.error('用户认证模块测试失败:', error);
      results.tests.push({
        name: '用户认证模块异常',
        error: error.message
      });
    }

    return results;
  }

  /**
   * 测试支付模块接口
   */
  async testPaymentApis() {
    console.log('=== 测试支付模块接口 ===');
    
    const results = {
      module: '支付模块',
      tests: []
    };

    try {
      // 测试获取会员套餐列表（不需要登录）
      console.log('测试获取会员套餐列表...');
      const plansResult = await paymentApi.getMembershipPlans();
      results.tests.push({
        name: '获取会员套餐列表',
        url: '/payment/plans',
        method: 'GET',
        success: plansResult.success !== false,
        response: plansResult
      });

      // 测试获取安全提示（不需要登录）
      console.log('测试获取安全提示...');
      const securityTipsResult = await paymentApi.getSecurityTips();
      results.tests.push({
        name: '获取安全提示',
        url: '/payment/security/security-tips',
        method: 'GET',
        success: securityTipsResult.success !== false,
        response: securityTipsResult
      });

      // 测试获取用户订单列表（需要登录）
      console.log('测试获取用户订单列表...');
      const ordersResult = await paymentApi.getUserOrders();
      results.tests.push({
        name: '获取用户订单列表',
        url: '/payment/orders',
        method: 'GET',
        success: ordersResult.success !== false,
        response: ordersResult
      });

    } catch (error) {
      console.error('支付模块测试失败:', error);
      results.tests.push({
        name: '支付模块异常',
        error: error.message
      });
    }

    return results;
  }

  /**
   * 测试会员管理模块接口
   */
  async testMembershipApis() {
    console.log('=== 测试会员管理模块接口 ===');
    
    const results = {
      module: '会员管理模块',
      tests: []
    };

    try {
      // 测试获取会员状态
      console.log('测试获取会员状态...');
      const statusResult = await membershipApi.getMembershipStatus();
      results.tests.push({
        name: '获取会员状态',
        url: '/membership/status',
        method: 'GET',
        success: statusResult.success !== false,
        response: statusResult
      });

      // 测试获取会员权益
      console.log('测试获取会员权益...');
      const benefitsResult = await membershipApi.getMembershipBenefits();
      results.tests.push({
        name: '获取会员权益',
        url: '/membership/benefits',
        method: 'GET',
        success: benefitsResult.success !== false,
        response: benefitsResult
      });

      // 测试检查功能权限
      console.log('测试检查功能权限...');
      const permissionResult = await membershipApi.checkFeaturePermission('resume_export');
      results.tests.push({
        name: '检查功能权限',
        url: '/membership/check-permission/resume_export',
        method: 'GET',
        success: permissionResult.success !== false,
        response: permissionResult
      });

    } catch (error) {
      console.error('会员管理模块测试失败:', error);
      results.tests.push({
        name: '会员管理模块异常',
        error: error.message
      });
    }

    return results;
  }

  /**
   * 测试卡密系统模块接口
   */
  async testCardApis() {
    console.log('=== 测试卡密系统模块接口 ===');
    
    const results = {
      module: '卡密系统模块',
      tests: []
    };

    try {
      // 测试获取卡密权益
      console.log('测试获取卡密权益...');
      const benefitsResult = await cardApi.getCardBenefits();
      results.tests.push({
        name: '获取卡密权益',
        url: '/cards/benefits',
        method: 'GET',
        success: benefitsResult.success !== false,
        response: benefitsResult
      });

      // 测试检查次数权益
      console.log('测试检查次数权益...');
      const quotaResult = await cardApi.checkQuotaAvailability('resume_export');
      results.tests.push({
        name: '检查次数权益',
        url: '/cards/quota/check',
        method: 'GET',
        success: quotaResult.success !== false,
        response: quotaResult
      });

      // 测试获取权限汇总
      console.log('测试获取权限汇总...');
      const summaryResult = await cardApi.getPermissionSummary();
      results.tests.push({
        name: '获取权限汇总',
        url: '/permissions/summary',
        method: 'GET',
        success: summaryResult.success !== false,
        response: summaryResult
      });

    } catch (error) {
      console.error('卡密系统模块测试失败:', error);
      results.tests.push({
        name: '卡密系统模块异常',
        error: error.message
      });
    }

    return results;
  }

  /**
   * 测试证件照生成模块接口
   */
  async testIdPhotoApis() {
    console.log('=== 测试证件照生成模块接口 ===');
    
    const results = {
      module: '证件照生成模块',
      tests: []
    };

    try {
      // 测试健康检查（不需要登录）
      console.log('测试证件照服务健康检查...');
      const healthResult = await idPhotoApiNew.healthCheck();
      results.tests.push({
        name: '证件照服务健康检查',
        url: '/idphoto/health',
        method: 'GET',
        success: healthResult.success !== false,
        response: healthResult
      });

      // 测试获取尺寸列表（不需要登录）
      console.log('测试获取证件照尺寸列表...');
      const sizesResult = await idPhotoApiNew.getSizes();
      results.tests.push({
        name: '获取证件照尺寸列表',
        url: '/idphoto/sizes',
        method: 'GET',
        success: sizesResult.success !== false,
        response: sizesResult
      });

      // 测试获取颜色列表（不需要登录）
      console.log('测试获取证件照颜色列表...');
      const colorsResult = await idPhotoApiNew.getColors();
      results.tests.push({
        name: '获取证件照颜色列表',
        url: '/idphoto/colors',
        method: 'GET',
        success: colorsResult.success !== false,
        response: colorsResult
      });

    } catch (error) {
      console.error('证件照生成模块测试失败:', error);
      results.tests.push({
        name: '证件照生成模块异常',
        error: error.message
      });
    }

    return results;
  }

  /**
   * 测试简历处理模块接口
   */
  async testResumeApis() {
    console.log('=== 测试简历处理模块接口 ===');
    
    const results = {
      module: '简历处理模块',
      tests: []
    };

    try {
      // 测试获取模板列表（不需要登录）
      console.log('测试获取简历模板列表...');
      const templatesResult = await resumeApi.getTemplates();
      results.tests.push({
        name: '获取简历模板列表',
        url: '/resume/templates',
        method: 'GET',
        success: templatesResult.success !== false,
        response: templatesResult
      });

      // 测试获取临时文件统计（不需要登录）
      console.log('测试获取临时文件统计...');
      const statsResult = await resumeApi.getTempFilesStats();
      results.tests.push({
        name: '获取临时文件统计',
        url: '/resume/temp-files/stats',
        method: 'GET',
        success: statsResult.success !== false,
        response: statsResult
      });

    } catch (error) {
      console.error('简历处理模块测试失败:', error);
      results.tests.push({
        name: '简历处理模块异常',
        error: error.message
      });
    }

    return results;
  }

  /**
   * 运行所有API测试
   */
  async runAllTests() {
    console.log('=== 开始API接口测试 ===');
    
    const allResults = [];

    try {
      // 测试各个模块
      allResults.push(await this.testUserAuthApis());
      allResults.push(await this.testPaymentApis());
      allResults.push(await this.testMembershipApis());
      allResults.push(await this.testCardApis());
      allResults.push(await this.testIdPhotoApis());
      allResults.push(await this.testResumeApis());

      // 生成测试报告
      this.generateTestReport(allResults);

    } catch (error) {
      console.error('API测试过程中发生错误:', error);
    }

    return allResults;
  }

  /**
   * 生成测试报告
   */
  generateTestReport(results) {
    console.log('=== API接口测试报告 ===');
    
    let totalTests = 0;
    let successTests = 0;
    let failedTests = 0;

    results.forEach(moduleResult => {
      console.log(`\n模块: ${moduleResult.module}`);
      
      moduleResult.tests.forEach(test => {
        totalTests++;
        
        if (test.success) {
          successTests++;
          console.log(`✅ ${test.name} - 成功`);
        } else {
          failedTests++;
          console.log(`❌ ${test.name} - 失败`);
          if (test.error) {
            console.log(`   错误: ${test.error}`);
          }
        }
      });
    });

    console.log('\n=== 测试统计 ===');
    console.log(`总测试数: ${totalTests}`);
    console.log(`成功: ${successTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${((successTests / totalTests) * 100).toFixed(2)}%`);
  }
}

module.exports = new ApiTester();
