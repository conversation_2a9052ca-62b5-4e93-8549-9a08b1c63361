const paymentApi = require('../../../utils/api/paymentApi.js');

Page({
  data: {
    orderInfo: {},
    expireTime: ''
  },

  onLoad(options) {
    const { orderId } = options;
    if (orderId) {
      this.loadOrderDetail(orderId);
    }
  },

  // 加载订单详情
  async loadOrderDetail(orderId) {
    wx.showLoading({ title: '加载中...' });

    try {
      const result = await paymentApi.getOrderDetail(orderId);

      if (result.success !== false) {
        const orderInfo = {
          ...result,
          create_time: this.formatTime(result.created_at),
          pay_time: result.paid_at ? this.formatTime(result.paid_at) : null,
          status_text: this.getStatusText(result.status),
          amount_yuan: result.amount_yuan || (result.amount / 100) // 确保有元为单位的金额
        };

        // 计算过期时间
        let expireTime = '';
        if (orderInfo.status === 'pending' && orderInfo.expired_at) {
          const expireDate = new Date(orderInfo.expired_at);
          const now = new Date();
          const diffMinutes = Math.floor((expireDate - now) / (1000 * 60));

          if (diffMinutes > 0) {
            const hours = Math.floor(diffMinutes / 60);
            const minutes = diffMinutes % 60;
            expireTime = `${hours}小时${minutes}分钟`;
          } else {
            expireTime = '已过期';
          }
        }

        this.setData({
          orderInfo,
          expireTime
        });
      } else {
        throw new Error(result.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'cancelled': '已取消',
      'expired': '已过期',
      'refunded': '已退款'
    };
    return statusMap[status] || '未知状态';
  },

  // 复制订单号
  copyOrderNo() {
    wx.setClipboardData({
      data: this.data.orderInfo.order_no,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 重新支付
  async repay() {
    const { orderInfo } = this.data;

    wx.showLoading({ title: '处理中...' });

    try {
      // 创建微信支付参数
      const wxPayResult = await paymentApi.createWxPayment(orderInfo.id);

      if (wxPayResult.success === false) {
        throw new Error(wxPayResult.message || '创建支付参数失败');
      }

      const paymentResult = await paymentApi.requestWxPayment(wxPayResult);

      if (paymentResult.success) {
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 刷新订单状态
        setTimeout(() => {
          this.loadOrderDetail(orderInfo.id);
        }, 1500);
      } else if (!paymentResult.cancelled) {
        throw new Error(paymentResult.message || '支付失败');
      }

    } catch (error) {
      console.error('支付失败:', error);
      wx.showToast({
        title: error.message || '支付失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
  },

  // 取消订单
  async cancelOrder() {
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: resolve
      });
    });

    if (!result.confirm) return;

    wx.showLoading({ title: '处理中...' });

    try {
      // 调用取消订单API
      const cancelResult = await paymentApi.cancelOrder(this.data.orderInfo.id, '用户主动取消');

      if (cancelResult.success === false) {
        throw new Error(cancelResult.message || '取消订单失败');
      }

      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });

      // 刷新订单状态
      setTimeout(() => {
        this.loadOrderDetail(this.data.orderInfo.id);
      }, 1500);
    } catch (error) {
      console.error('取消订单失败:', error);
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
  },

  // 联系客服
  contactService() {
    wx.setClipboardData({
      data: 'km80048',
      success: () => {
        wx.showToast({
          title: '客服微信已复制',
          icon: 'success'
        });
      }
    });
  }
});
