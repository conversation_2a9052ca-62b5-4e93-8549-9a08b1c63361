const app = getApp();
const paymentApi = require('../../../utils/api/paymentApi.js');
const membershipApi = require('../../../utils/api/membershipApi.js');
const membershipManager = require('../../../utils/user/membershipManager.js');

Page({
  data: {
    membershipPlans: [],
    selectedPlan: null,
    selectedPlanInfo: {},
    showPaymentModal: false,
    paymentLoading: false,
    benefits: [
      {
        icon: '/pages/payment/membership/images/unlimited.png',
        text: '无限制生成PDF'
      },
      {
        icon: '/pages/payment/membership/images/hd.png',
        text: '高清简历导出'
      },
      {
        icon: '/pages/payment/membership/images/template.png',
        text: '专属简历模板'
      },
      {
        icon: '/pages/payment/membership/images/support.png',
        text: '优先客服支持'
      }
    ]
  },

  onLoad() {
    this.loadMembershipPlans();
  },

  // 加载会员套餐
  async loadMembershipPlans() {
    wx.showLoading({ title: '加载中...' });

    try {
      const result = await paymentApi.getMembershipPlans();

      if (result.success !== false && result.plans) {
        // 使用新API返回的套餐数据，包含更丰富的信息
        const formattedPlans = result.plans.map(plan => ({
          id: plan.id,
          name: plan.name,
          description: plan.description,
          duration: this.formatDuration(plan.duration_days),
          price: plan.price_yuan, // 使用元为单位的价格
          original_price: plan.original_price_yuan, // 原价
          discount: plan.discount_rate ? `${(plan.discount_rate * 100).toFixed(0)}折` : null,
          features: plan.features || [], // 功能特性
          is_recommended: plan.is_recommended || false,
          sort_order: plan.sort_order || 0,
          duration_days: plan.duration_days
        }));

        // 按sort_order排序
        formattedPlans.sort((a, b) => a.sort_order - b.sort_order);

        this.setData({
          membershipPlans: formattedPlans
        });
      } else {
        // 使用默认套餐数据作为备用
        console.warn('获取套餐数据失败，使用默认数据:', result.message);
        this.setData({
          membershipPlans: this.getDefaultPlans()
        });
      }
    } catch (error) {
      console.error('加载套餐失败:', error);
      this.setData({
        membershipPlans: this.getDefaultPlans()
      });
    }

    wx.hideLoading();
  },

  // 格式化套餐时长
  formatDuration(durationDays) {
    if (durationDays <= 0) {
      return '永久有效';
    } else if (durationDays < 30) {
      return `${durationDays}天`;
    } else if (durationDays < 365) {
      const months = Math.round(durationDays / 30);
      return `${months}个月`;
    } else {
      const years = Math.round(durationDays / 365);
      return `${years}年`;
    }
  },

  // 获取默认套餐数据（备用）
  getDefaultPlans() {
    return [
      {
        id: 1,
        name: '月度会员',
        duration: '1个月',
        price: 19.9,
        original_price: null,
        description: '适合短期使用',
        discount: null,
        duration_days: 30,
        features: ['无限制生成PDF', '高清简历导出'],
        is_recommended: false
      },
      {
        id: 2,
        name: '季度会员',
        duration: '3个月',
        price: 49.9,
        original_price: 59.7,
        description: '性价比之选',
        discount: '84折',
        duration_days: 90,
        features: ['无限制生成PDF', '高清简历导出', '专属简历模板'],
        is_recommended: true
      },
      {
        id: 3,
        name: '年度会员',
        duration: '12个月',
        price: 168.0,
        original_price: 238.8,
        description: '最超值选择',
        discount: '70折',
        duration_days: 365,
        features: ['无限制生成PDF', '高清简历导出', '专属简历模板', '优先客服支持'],
        is_recommended: false
      }
    ];
  },

  // 选择套餐
  selectPlan(e) {
    const plan = e.currentTarget.dataset.plan;
    this.setData({
      selectedPlan: plan.id,
      selectedPlanInfo: plan
    });
  },

  // 创建订单
  createOrder() {
    if (!this.data.selectedPlan) {
      wx.showToast({
        title: '请选择套餐',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showPaymentModal: true
    });
  },

  // 隐藏支付弹窗
  hidePaymentModal() {
    this.setData({
      showPaymentModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 确认支付
  async confirmPayment() {
    const { selectedPlanInfo } = this.data;

    this.setData({ paymentLoading: true });

    try {
      // 创建订单
      const orderResult = await paymentApi.createOrder({
        plan_id: selectedPlanInfo.id,
        client_ip: '', // 小程序无法获取真实IP，由服务端处理
        user_agent: 'WeChat MiniProgram'
      });

      if (orderResult.success === false || !orderResult.id) {
        throw new Error(orderResult.message || '创建订单失败');
      }

      // 创建微信支付参数
      const wxPayResult = await paymentApi.createWxPayment(orderResult.id);

      if (wxPayResult.success === false || !wxPayResult.appId) {
        throw new Error(wxPayResult.message || '创建支付参数失败');
      }

      // 发起微信支付
      const paymentResult = await paymentApi.requestWxPayment(wxPayResult);

      if (paymentResult.success) {
        // 支付成功，同步会员状态
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 使用新的会员状态同步机制
        await this.syncMembershipStatus();

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else if (!paymentResult.cancelled) {
        throw new Error(paymentResult.message || '支付失败');
      }

    } catch (error) {
      console.error('支付流程失败:', error);
      wx.showToast({
        title: error.message || '支付失败',
        icon: 'none'
      });
    }

    this.setData({
      paymentLoading: false,
      showPaymentModal: false
    });
  },

  // 同步会员状态
  async syncMembershipStatus() {
    try {
      // 先尝试同步会员状态
      const syncResult = await membershipApi.syncMembershipStatus();
      console.log('会员状态同步结果:', syncResult);

      // 然后获取最新的会员状态
      const statusResult = await membershipApi.getMembershipStatus();
      if (statusResult.success !== false) {
        // 更新全局会员状态
        app.globalData.isMember = statusResult.is_member || false;

        if (statusResult.current_membership) {
          app.globalData.membershipExpiry = statusResult.current_membership.end_date;
        }

        // 强制刷新本地缓存
        membershipManager.queryMemberStatus(true);

        console.log('会员状态已更新:', {
          isMember: app.globalData.isMember,
          expiry: app.globalData.membershipExpiry
        });
      }
    } catch (error) {
      console.error('同步会员状态失败:', error);
      // 即使同步失败，也不影响支付成功的提示
    }
  },

  // 获取套餐月数（保留用于兼容性）
  getPlanMonths(planId) {
    const plan = this.data.membershipPlans.find(p => p.id === planId);
    if (plan && plan.duration_days) {
      if (plan.duration_days <= 0) return 999; // 永久
      return Math.round(plan.duration_days / 30);
    }

    // 备用映射
    const monthsMap = {
      'monthly': 1,
      'quarterly': 3,
      'yearly': 12,
      'permanent': 999
    };
    return monthsMap[planId] || 1;
  }
});