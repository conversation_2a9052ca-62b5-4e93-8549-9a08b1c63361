# 多商户系统API接口文档

## 概述

本系统支持多商户架构，允许不同的商户使用独立的微信小程序和支付配置。每个商户的数据完全隔离，确保安全性和独立性。

## 商户标识

### source参数

所有API请求都需要包含商户标识，支持以下几种方式：

1. **查询参数** (推荐)
```
GET /auth/login?source=merchant_code
POST /payment/create?source=merchant_code
```

2. **请求头**
```
X-Merchant-Source: merchant_code
```

3. **路径参数** (特定接口)
```
POST /payment/callback/merchant_code
```

4. **默认商户**
如果未提供source参数，系统将使用默认商户 `default`

### 商户代码规范

- 长度：2-50个字符
- 格式：字母、数字、下划线、连字符
- 示例：`default`, `company_a`, `shop-001`

## 认证接口

### 微信登录

**接口**: `POST /auth/login`

**请求参数**:
```json
{
  "code": "微信登录code",
  "user_info": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1,
    "country": "中国",
    "province": "北京",
    "city": "北京"
  }
}
```

**查询参数**:
- `source`: 商户代码 (必需)

**示例请求**:
```bash
curl -X POST "https://api.example.com/auth/login?source=company_a" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "wx_login_code_here",
    "user_info": {
      "nickName": "张三",
      "avatarUrl": "https://example.com/avatar.jpg"
    }
  }'
```

**响应**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "user_info": {
    "id": 123,
    "nickname": "张三",
    "avatar_url": "https://example.com/avatar.jpg",
    "is_member": false
  }
}
```

## 支付接口

### 创建订单

**接口**: `POST /payment/orders`

**查询参数**:
- `source`: 商户代码 (必需)

**请求参数**:
```json
{
  "plan_id": 1,
  "client_ip": "***********"
}
```

### 微信支付

**接口**: `POST /payment/wechat-pay`

**查询参数**:
- `source`: 商户代码 (必需)

### 支付回调

支持两种回调URL格式：

1. **通用回调** (系统自动识别商户)
```
POST /payment/callback
```

2. **指定商户回调** (推荐)
```
POST /payment/callback/{merchant_code}
```

## 商户管理接口

### 创建商户

**接口**: `POST /merchant/`

**权限**: 需要管理员权限

**请求参数**:
```json
{
  "merchant_code": "company_a",
  "merchant_name": "A公司",
  "description": "A公司的商户配置",
  "contact_name": "张经理",
  "contact_phone": "***********",
  "contact_email": "<EMAIL>",
  "business_config": {
    "industry": "education",
    "region": "beijing"
  }
}
```

### 获取商户列表

**接口**: `GET /merchant/`

**权限**: 需要管理员权限

**查询参数**:
- `include_inactive`: 是否包含停用商户 (默认: false)

### 创建微信配置

**接口**: `POST /merchant/{merchant_code}/wechat-config`

**权限**: 需要管理员权限

**请求参数**:
```json
{
  "app_id": "wx1234567890abcdef",
  "app_secret": "app_secret_here",
  "app_name": "A公司小程序",
  "mch_id": "1234567890",
  "api_key": "api_key_here",
  "api_v3_key": "api_v3_key_here",
  "cert_path": "certs/company_a_cert.pem",
  "key_path": "certs/company_a_key.pem",
  "cert_serial_no": "cert_serial_no_here",
  "notify_url": "https://api.example.com/payment/callback/company_a",
  "is_sandbox": false,
  "order_expire_minutes": 30,
  "order_prefix": "CA"
}
```

## 数据隔离

### 用户数据

- 每个商户的用户数据完全隔离
- 同一个微信openid可以在不同商户下创建不同的用户记录
- 用户只能访问自己所属商户的数据

### 订单数据

- 订单按商户隔离
- 订单号前缀可以按商户配置
- 支付回调自动路由到正确的商户配置

### 会员数据

- 会员套餐按商户配置
- 会员权益在商户内有效
- 会员状态不跨商户共享

## 错误处理

### 商户相关错误

```json
{
  "detail": "无效的商户标识: invalid_merchant"
}
```

```json
{
  "detail": "商户配置无效"
}
```

```json
{
  "detail": "缺少商户上下文"
}
```

## 最佳实践

### 1. 商户标识传递

推荐在所有API请求中使用查询参数传递商户标识：

```javascript
// 微信小程序示例
const merchantCode = 'company_a';

wx.request({
  url: `https://api.example.com/auth/login?source=${merchantCode}`,
  method: 'POST',
  data: {
    code: loginCode,
    user_info: userInfo
  },
  success: (res) => {
    // 处理登录成功
  }
});
```

### 2. 支付回调配置

为每个商户配置专用的回调URL：

```
https://api.example.com/payment/callback/company_a
https://api.example.com/payment/callback/company_b
```

### 3. 错误处理

```javascript
wx.request({
  url: `https://api.example.com/payment/orders?source=${merchantCode}`,
  method: 'POST',
  data: orderData,
  success: (res) => {
    // 处理成功
  },
  fail: (err) => {
    if (err.statusCode === 400 && err.data.detail.includes('商户')) {
      // 处理商户相关错误
      console.error('商户配置错误:', err.data.detail);
    }
  }
});
```

### 4. 配置管理

- 为每个商户准备独立的微信小程序
- 配置独立的微信支付商户号
- 设置专用的支付回调URL
- 定期检查商户配置的有效性

## 迁移指南

### 从单商户迁移

1. **运行迁移脚本**
```bash
python migrate_multi_merchant.py --default-merchant-code=your_company
```

2. **更新客户端代码**
在所有API请求中添加source参数

3. **配置商户信息**
使用管理员接口配置商户的微信配置

4. **测试验证**
确保所有功能在多商户环境下正常工作

### 添加新商户

1. **创建商户**
```bash
curl -X POST "https://api.example.com/merchant/" \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "merchant_code": "new_company",
    "merchant_name": "新公司"
  }'
```

2. **配置微信信息**
```bash
curl -X POST "https://api.example.com/merchant/new_company/wechat-config" \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "app_id": "wx_new_app_id",
    "app_secret": "new_app_secret",
    "mch_id": "new_mch_id",
    "api_v3_key": "new_api_v3_key",
    "notify_url": "https://api.example.com/payment/callback/new_company"
  }'
```

3. **客户端集成**
在新的微信小程序中使用新的商户代码

## 安全考虑

### 1. 商户验证

- 系统会验证商户的有效性
- 支持IP白名单限制
- API密钥验证（可选）

### 2. 数据隔离

- 严格的数据库级别隔离
- 中间件层面的访问控制
- 防止跨商户数据泄露

### 3. 配置安全

- 敏感配置信息加密存储
- 配置访问权限控制
- 定期配置审计

## 监控和日志

### 1. 商户API调用日志

系统会记录每个商户的API调用情况：
- 请求路径和方法
- 请求参数和响应
- 响应时间和状态
- 错误信息

### 2. 商户配置变更日志

- 配置创建和更新记录
- 操作人员和时间
- 变更前后对比

### 3. 性能监控

- 按商户统计API性能
- 商户配置缓存命中率
- 数据库查询性能分析
