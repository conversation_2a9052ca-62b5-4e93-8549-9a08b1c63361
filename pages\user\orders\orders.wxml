<view class="container">
  <!-- 订单列表 -->
  <view wx:if="{{orderList.length > 0}}" class="orders-list">
    <view class="order-item" wx:for="{{orderList}}" wx:key="id" bindtap="goToOrderDetail" data-order="{{item}}">
      <view class="order-header">
        <text class="order-number">订单号：{{item.order_no}}</text>
        <text class="order-status {{item.status}}">{{item.status_text}}</text>
      </view>
      
      <view class="order-content">
        <view class="product-info">
          <text class="product-name">{{item.product_name}}</text>
          <text class="product-duration">{{item.duration}}</text>
        </view>
        <view class="order-amount">¥{{item.amount}}</view>
      </view>
      
      <view class="order-footer">
        <text class="order-time">{{item.create_time}}</text>
        <view class="order-actions">
          <button wx:if="{{item.status === 'pending'}}" class="action-btn pay-btn" bindtap="repay" data-order="{{item}}" catchtap="stopPropagation">
            重新支付
          </button>
          <button wx:if="{{item.status === 'pending'}}" class="action-btn cancel-btn" bindtap="cancelOrder" data-order="{{item}}" catchtap="stopPropagation">
            取消订单
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <image class="empty-icon" src="/pages/user/orders/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无订单记录</text>
    <button class="go-buy-btn" bindtap="goToBuy">去开通会员</button>
  </view>
</view>