/**
 * 用户操作上报工具
 * 实现新的权益消耗流程，使用/user/action/report接口主动上报消耗
 */

const request = require('../api/request.js');

/**
 * 用户操作上报API类
 */
class ActionReporter {

  /**
   * 验证操作权限（预检查）
   * 对应接口: POST /user/action/validate-permission
   * @param {string} actionType 操作类型，如 'download_pdf'
   * @param {boolean} checkQuota 是否检查配额，默认true
   * @returns {Promise<Object>} 权限检查响应
   */
  async validatePermission(actionType, checkQuota = true) {
    try {
      console.log('=== 验证操作权限API请求 ===');
      console.log('操作类型:', actionType);
      console.log('检查配额:', checkQuota);

      const response = await request.post('/user/action/validate-permission', {
        action_type: actionType,
        check_quota: checkQuota
      }, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('验证操作权限失败:', error);
      return { 
        success: false, 
        message: '权限验证失败',
        data: {
          has_permission: false,
          reason: '网络错误或服务异常'
        }
      };
    }
  }

  /**
   * 上报用户操作（权益消耗）
   * 对应接口: POST /user/action/report
   * @param {Object} actionData 操作数据
   * @param {string} actionData.action_type 操作类型
   * @param {Object} actionData.action_content 操作相关内容（可选）
   * @param {string} actionData.feature_name 关联的功能名称（可选）
   * @param {string} actionData.resource_type 资源类型，如 'pdf'（可选）
   * @param {string} actionData.resource_id 资源ID（可选）
   * @param {number} actionData.file_size 文件大小(字节)（可选）
   * @param {string} actionData.file_format 文件格式（可选）
   * @param {string} actionData.operation_status 操作状态，默认'completed'（可选）
   * @param {string} actionData.error_message 如果操作失败，附带错误信息（可选）
   * @param {number} actionData.consumed_quota 消耗的配额，默认1（可选）
   * @param {Object} actionData.client_info 客户端信息（可选）
   * @param {string} actionData.ip_address 用户IP（可选）
   * @param {string} actionData.template_id 模板ID（可选）
   * @returns {Promise<Object>} 上报结果响应
   */
  async reportAction(actionData) {
    try {
      console.log('=== 上报用户操作API请求 ===');
      console.log('操作数据:', actionData);

      const response = await request.post('/user/action/report', {
        action_type: actionData.action_type,
        action_content: actionData.action_content || {},
        feature_name: actionData.feature_name || '',
        resource_type: actionData.resource_type || '',
        resource_id: actionData.resource_id || '',
        file_size: actionData.file_size || 0,
        file_format: actionData.file_format || '',
        operation_status: actionData.operation_status || 'completed',
        error_message: actionData.error_message || '',
        consumed_quota: actionData.consumed_quota || 1,
        client_info: actionData.client_info || this.getClientInfo(),
        ip_address: actionData.ip_address || '',
        template_id: actionData.template_id || ''
      }, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('上报用户操作失败:', error);
      return { 
        success: false, 
        message: '操作上报失败' 
      };
    }
  }

  /**
   * 获取用户操作统计
   * 对应接口: GET /user/action/stats
   * @param {number} days 统计最近N天的数据，默认30
   * @returns {Promise<Object>} 统计信息响应
   */
  async getActionStats(days = 30) {
    try {
      console.log('=== 获取用户操作统计API请求 ===');
      console.log('统计天数:', days);

      const response = await request.get(`/user/action/stats?days=${days}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取用户操作统计失败:', error);
      return { 
        success: false, 
        message: '获取统计信息失败' 
      };
    }
  }

  /**
   * 获取客户端信息
   * @returns {Object} 客户端信息
   */
  getClientInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        pixelRatio: systemInfo.pixelRatio,
        language: systemInfo.language,
        wifiEnabled: systemInfo.wifiEnabled,
        locationEnabled: systemInfo.locationEnabled,
        bluetoothEnabled: systemInfo.bluetoothEnabled,
        cameraAuthorized: systemInfo.cameraAuthorized,
        locationAuthorized: systemInfo.locationAuthorized,
        microphoneAuthorized: systemInfo.microphoneAuthorized,
        notificationAuthorized: systemInfo.notificationAuthorized,
        albumAuthorized: systemInfo.albumAuthorized,
        SDKVersion: systemInfo.SDKVersion,
        benchmarkLevel: systemInfo.benchmarkLevel
      };
    } catch (error) {
      console.error('获取客户端信息失败:', error);
      return {
        platform: 'unknown',
        error: error.message
      };
    }
  }

  /**
   * 简化的权限检查和操作上报流程
   * @param {string} actionType 操作类型
   * @param {Object} options 选项
   * @param {boolean} options.preCheck 是否预检查权限，默认true
   * @param {Object} options.actionData 操作数据
   * @param {Function} options.onPermissionDenied 权限被拒绝时的回调
   * @param {Function} options.onSuccess 操作成功时的回调
   * @param {Function} options.onError 操作失败时的回调
   * @returns {Promise<Object>} 操作结果
   */
  async executeWithReport(actionType, options = {}) {
    const {
      preCheck = true,
      actionData = {},
      onPermissionDenied,
      onSuccess,
      onError
    } = options;

    try {
      // 1. 预检查权限（可选）
      if (preCheck) {
        console.log('执行权限预检查...');
        const permissionResult = await this.validatePermission(actionType);
        
        if (permissionResult.success === false || !permissionResult.data?.has_permission) {
          const reason = permissionResult.data?.reason || permissionResult.message || '权限不足';
          console.log('权限检查失败:', reason);
          
          if (onPermissionDenied) {
            onPermissionDenied(permissionResult);
          }
          
          return {
            success: false,
            message: reason,
            type: 'permission_denied',
            data: permissionResult.data
          };
        }
        
        console.log('权限检查通过');
      }

      // 2. 执行实际操作（由调用方处理）
      // 这里只是标记操作开始
      console.log('开始执行操作:', actionType);

      // 3. 上报操作结果
      const reportData = {
        action_type: actionType,
        operation_status: 'completed',
        ...actionData
      };

      const reportResult = await this.reportAction(reportData);
      console.log('操作上报结果:', reportResult);

      if (onSuccess) {
        onSuccess(reportResult);
      }

      return {
        success: true,
        message: '操作完成并已上报',
        data: reportResult
      };

    } catch (error) {
      console.error('执行操作失败:', error);
      
      // 上报失败操作
      try {
        await this.reportAction({
          action_type: actionType,
          operation_status: 'failed',
          error_message: error.message,
          ...actionData
        });
      } catch (reportError) {
        console.error('上报失败操作失败:', reportError);
      }

      if (onError) {
        onError(error);
      }

      return {
        success: false,
        message: error.message || '操作失败',
        type: 'execution_error',
        error
      };
    }
  }

  /**
   * 常用操作类型的便捷方法
   */

  // PDF导出操作
  async reportPdfExport(templateId, fileSize, success = true, errorMessage = '') {
    return this.reportAction({
      action_type: 'download_pdf',
      feature_name: 'resume_export',
      resource_type: 'pdf',
      template_id: templateId,
      file_size: fileSize,
      file_format: 'pdf',
      operation_status: success ? 'completed' : 'failed',
      error_message: errorMessage
    });
  }

  // JPEG导出操作
  async reportJpegExport(templateId, fileSize, success = true, errorMessage = '') {
    return this.reportAction({
      action_type: 'download_jpeg',
      feature_name: 'resume_export',
      resource_type: 'jpeg',
      template_id: templateId,
      file_size: fileSize,
      file_format: 'jpeg',
      operation_status: success ? 'completed' : 'failed',
      error_message: errorMessage
    });
  }

  // 证件照生成操作
  async reportIdPhotoGenerate(photoSize, photoColor, fileSize, success = true, errorMessage = '') {
    return this.reportAction({
      action_type: 'generate_idphoto',
      feature_name: 'idphoto_generate',
      resource_type: 'image',
      file_size: fileSize,
      file_format: 'jpeg',
      action_content: {
        photo_size: photoSize,
        photo_color: photoColor
      },
      operation_status: success ? 'completed' : 'failed',
      error_message: errorMessage
    });
  }
}

module.exports = new ActionReporter();
