Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#4B8BF5",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconPath: "/images/tabbar/home.png",
        selectedIconPath: "/images/tabbar/home-active.png"
      },
      {
        pagePath: "/pages/user/center/center",
        text: "个人中心",
        iconPath: "/images/tabbar/user.png",
        selectedIconPath: "/images/tabbar/user-active.png"
      }
    ]
  },
  attached() {
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({url});
      this.setData({
        selected: data.index
      });
    }
  }
});
