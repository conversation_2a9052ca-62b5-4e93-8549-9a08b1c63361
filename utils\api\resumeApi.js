/**
 * 简历相关API接口
 */
const request = require('./request');
const apiConfig = require('../../config/apiConfig');
const actionReporter = require('../user/actionReporter');

/**
 * 生成简历预览图片（新版本 - 返回图片URL）
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @param {Object} options 可选参数
 * @returns {Promise<Object>} 包含图片URL的响应对象
 */
function generatePreviewImage(resumeData, themeConfig, templateId, options = {}) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId,
    // 添加图片优化参数
    quality: options.quality || 75,
    max_width: options.maxWidth || 1200,
    max_height: options.maxHeight || 3600
  };

  // 添加调试日志
  console.log('=== 预览图片API请求（新版本）===');
  console.log('模板ID:', templateId);
  console.log('主题配置:', themeConfig);
  console.log('图片参数:', { quality: requestData.quality, max_width: requestData.max_width, max_height: requestData.max_height });
  console.log('请求数据:', requestData);



  return request.request({
    url: apiConfig.exportJpegUrl,
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'text', // 改为text，因为现在返回JSON而不是二进制数据
    showLoading: false,
    showError: false,
    needAuth: true, // 根据文档，认证是可选的
    timeout: apiConfig.timeout?.previewImage || 10000 // 10秒超时
  });
}

/**
 * 生成简历预览图片（带权益消耗上报）
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @param {Object} options 选项
 * @param {boolean} options.preCheck 是否预检查权限，默认true
 * @param {Function} options.onPermissionDenied 权限被拒绝时的回调
 * @returns {Promise<Object>} 包含图片URL的响应对象
 */
async function generatePreviewImageWithReport(resumeData, themeConfig, templateId, options = {}) {
  const { preCheck = true, onPermissionDenied, ...imageOptions } = options;

  try {
    // 1. 预检查权限（可选）
    if (preCheck) {
      console.log('检查JPEG导出权限...');
      const permissionResult = await actionReporter.validatePermission('download_jpeg');

      if (permissionResult.success === false || !permissionResult.data?.has_permission) {
        const reason = permissionResult.data?.reason || permissionResult.message || '权限不足';
        console.log('JPEG导出权限检查失败:', reason);

        if (onPermissionDenied) {
          onPermissionDenied(permissionResult);
        }

        return {
          success: false,
          message: reason,
          type: 'permission_denied',
          data: permissionResult.data
        };
      }

      console.log('JPEG导出权限检查通过');
    }

    // 2. 执行图片生成
    console.log('开始生成JPEG...');
    const imageResult = await generatePreviewImage(resumeData, themeConfig, templateId, imageOptions);

    if (imageResult.success === false) {
      // 生成失败，上报失败操作
      await actionReporter.reportJpegExport(templateId, 0, false, imageResult.message);
      return imageResult;
    }

    // 3. 上报成功操作
    const fileSize = imageResult.file_size || 0;
    const reportResult = await actionReporter.reportJpegExport(templateId, fileSize, true);
    console.log('JPEG导出操作已上报:', reportResult);

    return {
      ...imageResult,
      action_reported: reportResult.success !== false
    };

  } catch (error) {
    console.error('JPEG生成失败:', error);

    // 上报失败操作
    try {
      await actionReporter.reportJpegExport(templateId, 0, false, error.message);
    } catch (reportError) {
      console.error('上报JPEG生成失败操作失败:', reportError);
    }

    return {
      success: false,
      message: error.message || 'JPEG生成失败',
      type: 'generation_error',
      error
    };
  }
}

/**
 * 生成PDF文件（新版本 - 返回PDF URL）
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @returns {Promise<Object>} 包含PDF URL的响应对象
 */
function generatePDF(resumeData, themeConfig, templateId) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId
  };

  // 添加调试日志
  console.log('=== PDF生成API请求（新版本）===');
  console.log('模板ID:', templateId);
  console.log('主题配置:', themeConfig);
  console.log('请求数据:', requestData);


  return request.request({
    url: apiConfig.generatePDFUrl,
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'text', // 改为text，因为现在返回JSON而不是二进制数据
    showLoading: false, // 关闭自动loading，由调用方控制
    showError: false, // 关闭自动错误提示，由调用方处理
    needAuth: true, // 根据文档，认证是可选的
    timeout: apiConfig.timeout?.generatePDF || 10000 // 10秒超时
  });
}

/**
 * 生成PDF文件（带权益消耗上报）
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @param {Object} options 选项
 * @param {boolean} options.preCheck 是否预检查权限，默认true
 * @param {Function} options.onPermissionDenied 权限被拒绝时的回调
 * @returns {Promise<Object>} 包含PDF URL的响应对象
 */
async function generatePDFWithReport(resumeData, themeConfig, templateId, options = {}) {
  const { preCheck = true, onPermissionDenied } = options;

  try {
    // 1. 预检查权限（可选）
    if (preCheck) {
      console.log('检查PDF导出权限...');
      const permissionResult = await actionReporter.validatePermission('download_pdf');

      if (permissionResult.success === false || !permissionResult.data?.has_permission) {
        const reason = permissionResult.data?.reason || permissionResult.message || '权限不足';
        console.log('PDF导出权限检查失败:', reason);

        if (onPermissionDenied) {
          onPermissionDenied(permissionResult);
        }

        return {
          success: false,
          message: reason,
          type: 'permission_denied',
          data: permissionResult.data
        };
      }

      console.log('PDF导出权限检查通过');
    }

    // 2. 执行PDF生成
    console.log('开始生成PDF...');
    const pdfResult = await generatePDF(resumeData, themeConfig, templateId);

    if (pdfResult.success === false) {
      // 生成失败，上报失败操作
      await actionReporter.reportPdfExport(templateId, 0, false, pdfResult.message);
      return pdfResult;
    }

    // 3. 上报成功操作
    const fileSize = pdfResult.file_size || 0;
    const reportResult = await actionReporter.reportPdfExport(templateId, fileSize, true);
    console.log('PDF导出操作已上报:', reportResult);

    return {
      ...pdfResult,
      action_reported: reportResult.success !== false
    };

  } catch (error) {
    console.error('PDF生成失败:', error);

    // 上报失败操作
    try {
      await actionReporter.reportPdfExport(templateId, 0, false, error.message);
    } catch (reportError) {
      console.error('上报PDF生成失败操作失败:', reportError);
    }

    return {
      success: false,
      message: error.message || 'PDF生成失败',
      type: 'generation_error',
      error
    };
  }
}

/**
 * 预览简历渲染结果，返回HTML内容用于调试
 * 对应接口: POST /resume/preview
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @returns {Promise<Object>} 预览HTML响应
 */
function previewResume(resumeData, themeConfig, templateId) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId
  };

  console.log('=== 简历预览API请求 ===');
  console.log('模板ID:', templateId);
  console.log('主题配置:', themeConfig);
  console.log('请求数据:', requestData);

  return request.request({
    url: '/resume/preview',
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 根据文档，认证是可选的
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取临时图片文件
 * 对应接口: GET /resume/temp-image/{file_id}
 * @param {string} fileId 文件唯一标识符
 * @returns {Promise<Object>} 图片文件响应
 */
function getTempImage(fileId) {
  console.log('=== 获取临时图片API请求 ===');
  console.log('文件ID:', fileId);

  return request.request({
    url: `/resume/temp-image/${fileId}`,
    method: 'GET',
    responseType: 'arraybuffer', // 返回二进制数据
    showLoading: false,
    showError: false,
    needAuth: false, // 根据文档不需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 手动清理过期的临时文件
 * 对应接口: POST /resume/cleanup-temp-files
 * @returns {Promise<Object>} 清理结果响应
 */
function cleanupTempFiles() {
  console.log('=== 清理临时文件API请求 ===');

  return request.request({
    url: '/resume/cleanup-temp-files',
    method: 'POST',
    data: {},
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: true, // 根据文档需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取临时文件服务的统计信息
 * 对应接口: GET /resume/temp-files/stats
 * @returns {Promise<Object>} 统计信息响应
 */
function getTempFilesStats() {
  console.log('=== 获取临时文件统计API请求 ===');

  return request.request({
    url: '/resume/temp-files/stats',
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 根据文档不需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取所有可用的简历模板列表
 * 对应接口: GET /resume/templates
 * @returns {Promise<Object>} 模板列表响应
 */
function getTemplates() {
  console.log('=== 获取简历模板列表API请求 ===');

  return request.request({
    url: '/resume/templates',
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 根据文档不需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

module.exports = {
  generatePreviewImage,
  generatePreviewImageWithReport,
  generatePDF,
  generatePDFWithReport,
  previewResume,
  getTempImage,
  cleanupTempFiles,
  getTempFilesStats,
  getTemplates
};
