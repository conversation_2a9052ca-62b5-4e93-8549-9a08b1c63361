const apiConfig = require('../../../config/apiConfig.js');

Page({
  data: {
    appName: apiConfig.wxAppName,
    appVersion: '1.0.0',
    features: [
      {
        icon: '/pages/user/about/images/template.png',
        name: '丰富模板',
        description: '多种精美简历模板，满足不同行业需求'
      },
      {
        icon: '/pages/user/about/images/edit.png',
        name: '在线编辑',
        description: '实时预览，所见即所得的编辑体验'
      },
      {
        icon: '/pages/user/about/images/export.png',
        name: '多格式导出',
        description: '支持PDF、图片等多种格式导出'
      },
      {
        icon: '/pages/user/about/images/cloud.png',
        name: '云端同步',
        description: '数据云端保存，随时随地访问编辑'
      }
    ],
    showPrivacyModal: false,
    showAgreementModal: false,
    privacyPolicy: `1. 信息收集
我们可能收集您提供的个人信息，包括但不限于：
- 基本信息（姓名、联系方式等）
- 简历内容和相关文档
- 使用行为数据

2. 信息使用
我们使用收集的信息用于：
- 提供简历制作服务
- 改善用户体验
- 客户服务支持

3. 信息保护
我们采取合理的安全措施保护您的个人信息：
- 数据加密传输和存储
- 严格的访问控制
- 定期安全审计

4. 信息共享
除法律要求外，我们不会向第三方分享您的个人信息。

5. 联系我们
如有隐私相关问题，请联系客服微信：km80048`,
    
    userAgreement: `1. 服务条款
欢迎使用本简历制作应用。使用本服务即表示您同意遵守以下条款。

2. 用户责任
- 提供真实、准确的个人信息
- 不得上传违法、有害内容
- 遵守相关法律法规

3. 服务内容
- 提供简历模板和编辑工具
- 支持多种格式导出
- 提供会员增值服务

4. 知识产权
- 应用及其内容受知识产权法保护
- 用户创建的简历内容归用户所有
- 模板设计版权归本应用所有

5. 免责声明
- 服务按"现状"提供，不保证无中断
- 不对求职结果承担责任
- 不承担因网络问题导致的损失

6. 服务变更
我们保留随时修改或终止服务的权利，会提前通知用户重大变更。

7. 争议解决
因使用本服务产生的争议，应通过友好协商解决。`
  },

  // 复制微信号
  copyWechat() {
    wx.setClipboardData({
      data: 'km80048',
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    this.setData({
      showPrivacyModal: true
    });
  },

  // 隐藏隐私政策
  hidePrivacyModal() {
    this.setData({
      showPrivacyModal: false
    });
  },

  // 显示用户协议
  showUserAgreement() {
    this.setData({
      showAgreementModal: true
    });
  },

  // 隐藏用户协议
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {}
});

