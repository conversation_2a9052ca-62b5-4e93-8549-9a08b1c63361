<!-- pages/user/center/center.wxml -->
<view class="container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 用户信息区域 -->
  <view class="user-section fade-in">
    <view class="user-card glass-card">
      <view class="user-info">
        <view class="avatar-container">
          <image class="avatar" src="{{userInfo.avatarUrl || '/pages/index/images/touXiang.png'}}" mode="aspectFill"></image>
          <view class="avatar-ring"></view>
        </view>
        <view class="user-detail">
          <text class="nickname">{{userInfo.nickName || '微信用户'}}</text>
          <text class="welcome">欢迎使用简历制作助手</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 会员状态区域 -->
  <view class="membership-section slide-up">
    <view class="membership-card glass-card {{isMember ? 'member-active' : 'non-member'}}">
      <view class="membership-header">
        <view class="membership-info">
          <image class="member-icon" src="/pages/user/center/images/{{isMember ? 'vip-active' : 'vip-inactive'}}.png" mode="aspectFit"></image>
          <view class="member-detail">
            <text class="member-status">{{membershipStatus}}</text>
            <text wx:if="{{memberExpireTime}}" class="expire-time">{{memberExpireTime}}</text>
            <text wx:if="{{isPermanentMember}}" class="permanent-badge">永久会员</text>
          </view>
        </view>

        <!-- 会员权益简化显示 -->
        <view wx:if="{{isMember}}" class="member-benefits-preview">
          <text class="benefits-count">{{memberBenefits.length}}项权益</text>
        </view>
      </view>

      <!-- 会员权益详情（可展开） -->
      <view wx:if="{{isMember && showBenefits}}" class="member-benefits">
        <view class="benefits-list">
          <view class="benefit-item" wx:for="{{memberBenefits}}" wx:key="index">
            <text class="benefit-icon">✓</text>
            <text class="benefit-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付按钮区域 -->
  <view class="payment-section scale-in">
    <button class="payment-btn glass-button {{isMember ? 'renew' : 'upgrade'}}" bindtap="upgradeMembership">
      <view class="btn-content">
        <text class="btn-text">{{isMember ? '续费会员' : '开通会员'}}</text>
        <text class="btn-subtitle">{{isMember ? '延长使用期限' : '解锁全部功能'}}</text>
      </view>
      <view class="btn-icon">💎</view>
    </button>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section slide-up">
    <!-- 主要功能卡片 -->
    <view class="main-menu-card glass-card">
      <view class="menu-title">
        <text class="title-text">我的服务</text>
        <text class="title-subtitle">个人中心功能</text>
      </view>

      <view class="menu-grid">
        <view class="menu-item" bindtap="redeemCard">
          <view class="menu-icon-wrapper">
            <text class="menu-emoji">🎫</text>
          </view>
          <text class="menu-text">兑换卡密</text>
        </view>

        <view class="menu-item" bindtap="goToOrders">
          <view class="menu-icon-wrapper">
            <text class="menu-emoji">📋</text>
          </view>
          <text class="menu-text">我的订单</text>
        </view>
      </view>
    </view>

    <!-- 辅助功能卡片 -->
    <view class="secondary-menu-card glass-card">
      <view class="menu-grid">
        <view class="menu-item" bindtap="goToFeedback">
          <view class="menu-icon-wrapper">
            <text class="menu-emoji">💬</text>
          </view>
          <text class="menu-text">意见反馈</text>
        </view>

        <view class="menu-item" bindtap="goToAbout">
          <view class="menu-icon-wrapper">
            <text class="menu-emoji">ℹ️</text>
          </view>
          <text class="menu-text">关于我们</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 为底部导航栏预留空间 -->
  <view class="tabbar-placeholder"></view>
</view>

<!-- 卡密兑换弹窗 -->
<view class="modal-overlay {{showRedeemModal ? 'show' : ''}}" bindtap="hideRedeemModal">
  <view class="redeem-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">兑换卡密</text>
      <image class="close-btn" src="/pages/user/center/images/close.png" bindtap="hideRedeemModal" mode="aspectFit"></image>
    </view>
    
    <view class="modal-content">
      <view class="purchase-info">
        <text class="info-title">如何购买卡密？</text>
        <text class="info-text">请联系客服微信：km80048 购买卡密</text>
      </view>
      
      <view class="redeem-form">
        <text class="form-label">请输入卡密：</text>
        <input class="card-input" placeholder="请输入16位卡密" value="{{cardCode}}" bindinput="onCardCodeInput" maxlength="16"></input>
        <button class="redeem-submit-btn" bindtap="submitRedeem" disabled="{{!cardCode}}">立即兑换</button>
      </view>
    </view>
  </view>
</view>

