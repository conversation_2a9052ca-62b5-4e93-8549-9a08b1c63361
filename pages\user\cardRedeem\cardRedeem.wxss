.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.card-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.card-input:focus {
  border-color: #4B8BF5;
  background: white;
}

.clear-btn {
  position: absolute;
  right: 10rpx;
  width: 60rpx;
  height: 60rpx;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  border: none;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
}

/* 验证结果 */
.validation-result {
  margin-bottom: 30rpx;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid;
}

.result-item.valid {
  background: #f0f9ff;
  border-color: #22c55e;
}

.result-item.invalid {
  background: #fef2f2;
  border-color: #ef4444;
}

.result-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 15rpx;
  margin-top: 5rpx;
}

.result-item.valid .result-icon {
  color: #22c55e;
}

.result-item.invalid .result-icon {
  color: #ef4444;
}

.result-content {
  flex: 1;
}

.result-text {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.result-detail {
  font-size: 24rpx;
  color: #666;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.validate-btn, .redeem-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.validate-btn {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e5e5e5;
}

.validate-btn:not([disabled]):active {
  background: #e9ecef;
}

.redeem-btn {
  background: linear-gradient(45deg, #4B8BF5, #6366f1);
  color: white;
}

.redeem-btn:not([disabled]):active {
  opacity: 0.8;
}

.validate-btn[disabled], .redeem-btn[disabled] {
  opacity: 0.5;
}

/* 测试区域 */
.test-section {
  text-align: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #e5e5e5;
}

.test-btn {
  background: #fbbf24;
  color: white;
  height: 60rpx;
  padding: 0 30rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

/* 权益区域 */
.benefits-section, .history-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
}

.benefit-card {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
}

.benefit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.benefit-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.benefit-value {
  font-size: 32rpx;
  color: #4B8BF5;
  font-weight: 600;
}

.benefit-status.active {
  background: #22c55e;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.quota-list {
  margin-top: 15rpx;
}

.quota-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
}

.quota-item:last-child {
  border-bottom: none;
}

.quota-text {
  font-size: 26rpx;
  color: #333;
}

.quota-expire {
  font-size: 22rpx;
  color: #666;
}

.membership-expire {
  font-size: 24rpx;
  color: #666;
}

.no-benefits {
  text-align: center;
  padding: 40rpx 0;
}

.no-benefits-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.no-benefits-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 历史记录 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.history-content {
  flex: 1;
}

.history-benefit {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.history-time {
  font-size: 22rpx;
  color: #666;
}

.history-status {
  margin-left: 20rpx;
}

.status-text.success {
  color: #22c55e;
  font-size: 24rpx;
  font-weight: 500;
}
