<view class="container">
  <view class="result-content">
    <!-- 支付成功 -->
    <view wx:if="{{status === 'success'}}" class="success-result">
      <image class="result-icon" src="/pages/payment/result/images/success.png" mode="aspectFit"></image>
      <text class="result-title">支付成功</text>
      <text class="result-desc">恭喜您成为会员，享受专属权益</text>
      
      <view class="order-info">
        <view class="info-row">
          <text class="info-label">订单号：</text>
          <text class="info-value">{{orderInfo.order_no}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">套餐：</text>
          <text class="info-value">{{orderInfo.product_name}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">金额：</text>
          <text class="info-value">¥{{orderInfo.amount}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">有效期：</text>
          <text class="info-value">{{membershipInfo}}</text>
        </view>
      </view>
    </view>

    <!-- 支付失败 -->
    <view wx:elif="{{status === 'failed'}}" class="failed-result">
      <image class="result-icon" src="/pages/payment/result/images/failed.png" mode="aspectFit"></image>
      <text class="result-title">支付失败</text>
      <text class="result-desc">{{failReason || '支付过程中出现问题，请重试'}}</text>
    </view>

    <!-- 支付取消 -->
    <view wx:else class="cancelled-result">
      <image class="result-icon" src="/pages/payment/result/images/cancelled.png" mode="aspectFit"></image>
      <text class="result-title">支付已取消</text>
      <text class="result-desc">您可以重新选择套餐进行支付</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button wx:if="{{status === 'success'}}" class="primary-btn" bindtap="goToUserCenter">
      查看会员权益
    </button>
    <button wx:else class="primary-btn" bindtap="retryPayment">
      重新支付
    </button>
    
    <button class="secondary-btn" bindtap="goHome">
      返回首页
    </button>
  </view>
</view>