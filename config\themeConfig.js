// 主题配置管理
const ThemeConfig = {
  // 当前主题
  currentTheme: 'default',
  
  // 主题配置
  themes: {
    default: {
      name: '默认主题',
      description: '温柔的莫兰迪色系，毛玻璃效果',
      colors: {
        primary: '#8BB6E8',
        primaryLight: '#A8C8ED',
        primaryDark: '#6B9BD1',
        secondary: '#D4B5A0',
        accent: '#C8A8A8',
        success: '#A8C4A2',
        warning: '#E8C5A0',
        error: '#D4A5A5'
      }
    },
    
    warm: {
      name: '暖色主题',
      description: '温暖的橙色系，适合秋冬使用',
      colors: {
        primary: '#E8B88B',
        primaryLight: '#EDC8A8',
        primaryDark: '#D19B6B',
        secondary: '#A0B5D4',
        accent: '#A8C8A8',
        success: '#A2C4A8',
        warning: '#A0C5E8',
        error: '#A5A5D4'
      }
    },
    
    cool: {
      name: '冷色主题',
      description: '清爽的蓝绿色系，适合春夏使用',
      colors: {
        primary: '#8BE8B6',
        primaryLight: '#A8EDC8',
        primaryDark: '#6BD19B',
        secondary: '#B5A0D4',
        accent: '#C8A8C8',
        success: '#C4A2A8',
        warning: '#C5A0E8',
        error: '#D4A5A5'
      }
    }
  },
  
  // 获取当前主题配置
  getCurrentTheme() {
    return this.themes[this.currentTheme] || this.themes.default;
  },
  
  // 切换主题
  switchTheme(themeName) {
    if (this.themes[themeName]) {
      this.currentTheme = themeName;
      this.applyTheme();
      // 保存到本地存储
      wx.setStorageSync('currentTheme', themeName);
      return true;
    }
    return false;
  },
  
  // 应用主题
  applyTheme() {
    const theme = this.getCurrentTheme();
    // 这里可以动态更新CSS变量
    // 由于小程序限制，主要通过重新设置页面样式实现
    console.log('应用主题:', theme.name);
  },
  
  // 初始化主题
  init() {
    const savedTheme = wx.getStorageSync('currentTheme');
    if (savedTheme && this.themes[savedTheme]) {
      this.currentTheme = savedTheme;
    }
    this.applyTheme();
  },
  
  // 获取所有可用主题
  getAllThemes() {
    return Object.keys(this.themes).map(key => ({
      key,
      ...this.themes[key]
    }));
  }
};

module.exports = ThemeConfig;
