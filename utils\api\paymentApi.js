/**
 * 支付相关API接口
 * 对应服务端API文档中的支付模块 (/payment) 和支付安全模块 (/payment/security)
 */
const request = require('./request.js');

/**
 * 支付相关API类
 */
class PaymentApi {

  /**
   * 获取可用的会员套餐列表
   * 对应接口: GET /payment/plans
   * @param {boolean} activeOnly 是否只获取启用的套餐，默认为true
   * @returns {Promise<Object>} 套餐列表响应
   */
  async getMembershipPlans(activeOnly = true) {
    try {
      console.log('=== 获取会员套餐列表API请求 ===');
      console.log('只获取启用套餐:', activeOnly);

      const queryParams = activeOnly ? '?active_only=true' : '';
      const response = await request.get(`/payment/plans${queryParams}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: false // 根据文档不需要认证
      });
      return response;
    } catch (error) {
      console.error('获取会员套餐失败:', error);
      return { success: false, message: '获取套餐信息失败' };
    }
  }

  /**
   * 创建会员购买订单
   * 对应接口: POST /payment/create-order
   * @param {Object} orderData 订单数据
   * @param {number} orderData.plan_id 套餐ID
   * @param {string} orderData.client_ip 客户端IP（可选）
   * @param {string} orderData.user_agent 用户代理（可选）
   * @returns {Promise<Object>} 订单信息响应
   */
  async createOrder(orderData) {
    try {
      console.log('=== 创建订单API请求 ===');
      console.log('订单数据:', orderData);

      const response = await request.post('/payment/create-order', orderData, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('创建订单失败:', error);
      return { success: false, message: '创建订单失败' };
    }
  }

  /**
   * 获取当前用户的订单列表
   * 对应接口: GET /payment/orders
   * @param {Object} params 查询参数
   * @param {string} params.status_filter 订单状态过滤（可选）
   * @param {number} params.limit 每页数量，默认20，最大100（可选）
   * @param {number} params.offset 偏移量，默认0（可选）
   * @returns {Promise<Object>} 订单列表响应
   */
  async getUserOrders(params = {}) {
    try {
      console.log('=== 获取用户订单列表API请求 ===');
      console.log('查询参数:', params);

      const queryParams = new URLSearchParams();
      if (params.status_filter) queryParams.append('status_filter', params.status_filter);
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.offset) queryParams.append('offset', params.offset);

      const queryString = queryParams.toString();
      const url = `/payment/orders${queryString ? '?' + queryString : ''}`;

      const response = await request.get(url, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取订单列表失败:', error);
      return { success: false, message: '获取订单列表失败' };
    }
  }

  /**
   * 获取指定订单的详细信息
   * 对应接口: GET /payment/order/{order_id}
   * @param {string} orderId 订单ID
   * @returns {Promise<Object>} 订单详情响应
   */
  async getOrderDetail(orderId) {
    try {
      console.log('=== 获取订单详情API请求 ===');
      console.log('订单ID:', orderId);

      const response = await request.get(`/payment/order/${orderId}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取订单详情失败:', error);
      return { success: false, message: '获取订单详情失败' };
    }
  }

  /**
   * 为指定订单创建微信支付参数
   * 对应接口: POST /payment/wechat-pay
   * @param {string} orderId 订单ID
   * @returns {Promise<Object>} 微信支付参数响应
   */
  async createWxPayment(orderId) {
    try {
      console.log('=== 创建微信支付API请求 ===');
      console.log('订单ID:', orderId);

      const response = await request.post('/payment/wechat-pay', { order_id: orderId }, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('创建微信支付失败:', error);
      return { success: false, message: '创建微信支付失败' };
    }
  }

  /**
   * 取消指定的未支付订单
   * 对应接口: POST /payment/cancel-order/{order_id}
   * @param {string} orderId 订单ID
   * @param {string} cancelReason 取消原因（可选）
   * @returns {Promise<Object>} 取消结果响应
   */
  async cancelOrder(orderId, cancelReason = '') {
    try {
      console.log('=== 取消订单API请求 ===');
      console.log('订单ID:', orderId);
      console.log('取消原因:', cancelReason);

      const requestData = cancelReason ? { cancel_reason: cancelReason } : {};
      const response = await request.post(`/payment/cancel-order/${orderId}`, requestData, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('取消订单失败:', error);
      return { success: false, message: '取消订单失败' };
    }
  }

  /**
   * 查询微信支付平台上的订单状态
   * 对应接口: GET /payment/query-order/{order_id}
   * @param {string} orderId 订单ID
   * @returns {Promise<Object>} 订单状态响应
   */
  async queryOrderStatus(orderId) {
    try {
      console.log('=== 查询订单状态API请求 ===');
      console.log('订单ID:', orderId);

      const response = await request.get(`/payment/query-order/${orderId}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('查询订单状态失败:', error);
      return { success: false, message: '查询订单状态失败' };
    }
  }

  /**
   * 发起微信支付
   * @param {Object} paymentData 支付数据
   * @param {string} paymentData.appId 小程序ID
   * @param {string} paymentData.timeStamp 时间戳
   * @param {string} paymentData.nonceStr 随机字符串
   * @param {string} paymentData.package 订单详情扩展字符串
   * @param {string} paymentData.signType 签名方式
   * @param {string} paymentData.paySign 签名
   * @returns {Promise<Object>} 支付结果
   */
  async requestWxPayment(paymentData) {
    return new Promise((resolve) => {
      console.log('=== 发起微信支付 ===');
      console.log('支付参数:', paymentData);

      wx.requestPayment({
        timeStamp: paymentData.timeStamp,
        nonceStr: paymentData.nonceStr,
        package: paymentData.package,
        signType: paymentData.signType,
        paySign: paymentData.paySign,
        success: (res) => {
          console.log('支付成功:', res);
          resolve({ success: true, data: res });
        },
        fail: (err) => {
          console.error('支付失败:', err);
          if (err.errMsg === 'requestPayment:fail cancel') {
            resolve({ success: false, message: '用户取消支付', cancelled: true });
          } else {
            resolve({ success: false, message: '支付失败', error: err });
          }
        }
      });
    });
  }

  // === 支付安全模块方法 ===

  /**
   * 检查当前用户是否有权限创建新订单
   * 对应接口: GET /payment/security/check-order-permission
   * @returns {Promise<Object>} 权限检查响应
   */
  async checkOrderPermission() {
    try {
      console.log('=== 检查订单创建权限API请求 ===');

      const response = await request.get('/payment/security/check-order-permission', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('检查订单权限失败:', error);
      return { success: false, message: '检查订单权限失败' };
    }
  }

  /**
   * 检查当前用户是否有权限支付指定订单
   * 对应接口: GET /payment/security/check-payment-permission/{order_id}
   * @param {string} orderId 订单ID
   * @returns {Promise<Object>} 支付权限检查响应
   */
  async checkPaymentPermission(orderId) {
    try {
      console.log('=== 检查支付权限API请求 ===');
      console.log('订单ID:', orderId);

      const response = await request.get(`/payment/security/check-payment-permission/${orderId}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('检查支付权限失败:', error);
      return { success: false, message: '检查支付权限失败' };
    }
  }

  /**
   * 获取支付安全提示信息
   * 对应接口: GET /payment/security/security-tips
   * @returns {Promise<Object>} 安全提示响应
   */
  async getSecurityTips() {
    try {
      console.log('=== 获取安全提示API请求 ===');

      const response = await request.get('/payment/security/security-tips', {}, {
        showLoading: false,
        showError: false,
        needAuth: false // 根据文档不需要认证
      });
      return response;
    } catch (error) {
      console.error('获取安全提示失败:', error);
      return { success: false, message: '获取安全提示失败' };
    }
  }
}

module.exports = new PaymentApi();