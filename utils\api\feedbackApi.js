/**
 * 用户反馈相关API接口
 * 对应服务端API文档中的用户反馈模块 (/feedback)
 */
const request = require('./request');

/**
 * 用户反馈API类
 */
class FeedbackApi {

  /**
   * 用户提交反馈信息
   * 对应接口: POST /feedback
   * @param {Object} feedbackData 反馈数据
   * @param {string} feedbackData.content 反馈内容，最多2000字符
   * @param {string} feedbackData.contact_info 联系方式（可选）
   * @returns {Promise<Object>} 提交结果响应
   */
  async submitFeedback(feedbackData) {
    try {
      console.log('=== 提交反馈API请求 ===');
      console.log('反馈数据:', feedbackData);

      const requestData = {
        content: feedbackData.content,
        contact_info: feedbackData.contact_info || feedbackData.contact || ''
      };

      const response = await request.post('/feedback', requestData, {
        showLoading: false,
        showError: false,
        needAuth: true // 根据文档需要认证
      });
      return response;
    } catch (error) {
      console.error('提交反馈失败:', error);
      return { success: false, message: '提交反馈失败' };
    }
  }

  /**
   * 获取当前用户的反馈列表
   * 对应接口: GET /feedback
   * @param {Object} params 查询参数
   * @param {string} params.status_filter 状态筛选，可选值: pending, replied, closed
   * @param {number} params.limit 返回数量限制，默认20，最大100
   * @param {number} params.offset 偏移量，默认0
   * @returns {Promise<Object>} 反馈列表响应
   */
  async getFeedbackList(params = {}) {
    try {
      console.log('=== 获取反馈列表API请求 ===');
      console.log('查询参数:', params);

      const queryParams = new URLSearchParams();
      if (params.status_filter) queryParams.append('status_filter', params.status_filter);
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.offset) queryParams.append('offset', params.offset.toString());

      const queryString = queryParams.toString();
      const url = `/feedback${queryString ? '?' + queryString : ''}`;

      const response = await request.get(url, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取反馈列表失败:', error);
      return { success: false, message: '获取反馈列表失败' };
    }
  }

  /**
   * 获取指定反馈的详细信息
   * 对应接口: GET /feedback/{feedback_id}
   * @param {number} feedbackId 反馈ID
   * @returns {Promise<Object>} 反馈详情响应
   */
  async getFeedbackDetail(feedbackId) {
    try {
      console.log('=== 获取反馈详情API请求 ===');
      console.log('反馈ID:', feedbackId);

      const response = await request.get(`/feedback/${feedbackId}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取反馈详情失败:', error);
      return { success: false, message: '获取反馈详情失败' };
    }
  }

  /**
   * 管理员回复用户反馈（需要管理员权限）
   * 对应接口: POST /feedback/{feedback_id}/reply
   * @param {number} feedbackId 反馈ID
   * @param {Object} replyData 回复数据
   * @param {string} replyData.reply_content 回复内容，最多2000字符
   * @param {string} replyData.admin_name 管理员名称
   * @returns {Promise<Object>} 回复结果响应
   */
  async replyFeedback(feedbackId, replyData) {
    try {
      console.log('=== 回复反馈API请求 ===');
      console.log('反馈ID:', feedbackId);
      console.log('回复数据:', replyData);

      const response = await request.post(`/feedback/${feedbackId}/reply`, replyData, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('回复反馈失败:', error);
      return { success: false, message: '回复反馈失败' };
    }
  }

  /**
   * 更新反馈状态（需要管理员权限）
   * 对应接口: PUT /feedback/{feedback_id}/status
   * @param {number} feedbackId 反馈ID
   * @param {string} status 新状态，可选值: pending, replied, closed
   * @returns {Promise<Object>} 更新结果响应
   */
  async updateFeedbackStatus(feedbackId, status) {
    try {
      console.log('=== 更新反馈状态API请求 ===');
      console.log('反馈ID:', feedbackId);
      console.log('新状态:', status);

      const response = await request.put(`/feedback/${feedbackId}/status`, { status }, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('更新反馈状态失败:', error);
      return { success: false, message: '更新反馈状态失败' };
    }
  }

  /**
   * 获取用户反馈统计信息
   * @returns {Promise<Object>} 反馈统计响应
   */
  async getFeedbackStats() {
    try {
      console.log('=== 获取反馈统计信息 ===');

      const listResult = await this.getFeedbackList({ limit: 100 });

      if (listResult.success !== false && listResult.items) {
        const items = listResult.items;
        const stats = {
          total: listResult.total || items.length,
          pending: items.filter(item => item.status === 'pending').length,
          replied: items.filter(item => item.status === 'replied').length,
          closed: items.filter(item => item.status === 'closed').length
        };

        return {
          success: true,
          data: stats,
          message: '获取反馈统计成功'
        };
      } else {
        return {
          success: false,
          message: '获取反馈统计失败'
        };
      }
    } catch (error) {
      console.error('获取反馈统计失败:', error);
      return { success: false, message: '获取反馈统计失败' };
    }
  }
}

module.exports = new FeedbackApi();
