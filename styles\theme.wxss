/* 全局主题配置文件 - 拟人化毛玻璃风格 */

/* ==================== 颜色变量定义 ==================== */

/* 主色调 - 莫兰迪色系 - 微信小程序使用page选择器定义CSS变量 */
page {
  /* 主色调 - 温柔蓝色系 */
  --primary-color: #8BB6E8;           /* 雾霾蓝 */
  --primary-light: #A8C8ED;           /* 浅雾霾蓝 */
  --primary-dark: #6B9BD1;            /* 深雾霾蓝 */
  --primary-gradient: linear-gradient(135deg, #8BB6E8 0%, #A8C8ED 100%);

  /* 辅助色调 - 温柔色彩 */
  --secondary-color: #D4B5A0;         /* 燕麦色 */
  --accent-color: #C8A8A8;            /* 豆沙粉 */
  --success-color: #A8C4A2;           /* 豆沙绿 */
  --warning-color: #E8C5A0;           /* 暖杏色 */
  --error-color: #D4A5A5;             /* 暗砖红 */

  /* 中性色调 - 高级灰系 */
  --neutral-50: #F8F9FA;              /* 极浅灰 */
  --neutral-100: #F1F3F4;             /* 浅灰 */
  --neutral-200: #E8EAED;             /* 中浅灰 */
  --neutral-300: #DADCE0;             /* 中灰 */
  --neutral-400: #BDC1C6;             /* 深中灰 */
  --neutral-500: #9AA0A6;             /* 深灰 */
  --neutral-600: #80868B;             /* 更深灰 */
  --neutral-700: #5F6368;             /* 深色灰 */
  --neutral-800: #3C4043;             /* 极深灰 */
  --neutral-900: #202124;             /* 黑色 */

  /* 文字颜色 */
  --text-primary: #3C4043;            /* 主要文字 */
  --text-secondary: #5F6368;          /* 次要文字 */
  --text-tertiary: #80868B;           /* 三级文字 */
  --text-disabled: #BDC1C6;           /* 禁用文字 */
  --text-white: #FFFFFF;              /* 白色文字 */

  /* 背景色 */
  --bg-primary: #FFFFFF;              /* 主背景 */
  --bg-secondary: #F8F9FA;            /* 次背景 */
  --bg-tertiary: #F1F3F4;             /* 三级背景 */
  --bg-overlay: rgba(255, 255, 255, 0.9); /* 覆盖层背景 */

  /* 毛玻璃效果变量 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8rpx 32rpx rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);

  /* 阴影系统 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);

  /* 圆角系统 */
  --radius-xs: 4rpx;
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-2xl: 24rpx;
  --radius-full: 50%;

  /* 间距系统 */
  --space-xs: 8rpx;
  --space-sm: 12rpx;
  --space-md: 16rpx;
  --space-lg: 24rpx;
  --space-xl: 32rpx;
  --space-2xl: 48rpx;
  --space-3xl: 64rpx;

  /* 字体大小 */
  --text-xs: 20rpx;
  --text-sm: 24rpx;
  --text-base: 28rpx;
  --text-lg: 32rpx;
  --text-xl: 36rpx;
  --text-2xl: 40rpx;
  --text-3xl: 48rpx;

  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;

  /* 动画缓动 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* 渐变背景 */
  --gradient-soft: linear-gradient(135deg, #F8F9FA 0%, #F1F3F4 100%);
}



/* ==================== 毛玻璃效果组件类 ==================== */

/* 基础毛玻璃效果 */
.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1rpx solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* 毛玻璃卡片 */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1rpx solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-lg);
  transition: all var(--duration-normal) var(--ease-out);
}

.glass-card:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-xl);
}

/* 毛玻璃按钮 */
.glass-button {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1rpx solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-xl);
  color: var(--text-primary);
  font-size: var(--text-base);
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-slow) var(--ease-out);
}

.glass-button:active::before {
  left: 100%;
}

/* ==================== 新拟物效果组件类 ==================== */

/* 新拟物基础效果 */
.neumorphism {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.6),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.5);
  transition: all var(--duration-normal) var(--ease-out);
}

.neumorphism:active {
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.6),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.5);
}

/* 新拟物按钮 */
.neumorphism-button {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-xl);
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
  border: none;
  color: var(--text-primary);
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-out);
}

.neumorphism-button:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.4),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.98);
}

/* 新拟物卡片 */
.neumorphism-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.5);
  transition: all var(--duration-normal) var(--ease-out);
}

.neumorphism-card:hover {
  transform: translateY(-2rpx);
  box-shadow:
    16rpx 16rpx 32rpx rgba(163, 177, 198, 0.6),
    -16rpx -16rpx 32rpx rgba(255, 255, 255, 0.5);
}

/* ==================== 增强磨砂玻璃效果 ==================== */

/* 强磨砂玻璃效果 */
.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  border-radius: var(--radius-xl);
  box-shadow:
    0 8rpx 32rpx rgba(31, 38, 135, 0.37),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

/* 彩色磨砂玻璃 */
.glass-colored {
  background: linear-gradient(135deg,
    rgba(139, 182, 232, 0.2) 0%,
    rgba(168, 200, 237, 0.15) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(139, 182, 232, 0.3);
  border-radius: var(--radius-xl);
  box-shadow:
    0 8rpx 32rpx rgba(139, 182, 232, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

/* 深色磨砂玻璃 */
.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

/* ==================== 渐变背景系统 ==================== */

.gradient-primary {
  background: var(--primary-gradient);
}

.gradient-soft {
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
}

.gradient-warm {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
}

/* ==================== 文字样式系统 ==================== */

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-white { color: var(--text-white); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* ==================== 布局工具类 ==================== */

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-1 { flex: 1; }

/* 间距工具类 */
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }

/* 圆角工具类 */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ==================== 动画效果 ==================== */

.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0rpx);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

/* ==================== 响应式断点 ==================== */

/* 小屏幕适配 */
@media (max-width: 750rpx) {
  page {
    --space-lg: 20rpx;
    --space-xl: 28rpx;
    --text-base: 26rpx;
    --text-lg: 30rpx;
  }
}
