<view class="container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
  </view>

  <!-- 页面标题 -->
  <view class="page-header fade-in">
    <view class="header-card glass-card">
      <view class="header-content">
        <text class="page-title">证件照制作</text>
        <text class="page-subtitle">AI智能抠图，一键生成专业证件照</text>
      </view>
      <view class="header-icon">📷</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section slide-up">
    <view class="loading-card glass-card">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载配置信息...</text>
    </view>
  </view>

  <!-- 尺寸选择区域 -->
  <view wx:else class="size-section slide-up">
    <view class="size-card glass-card">
      <view class="section-header">
        <text class="section-title">选择证件照尺寸</text>
        <text class="section-subtitle">选择适合的尺寸规格</text>
      </view>

      <view class="size-grid">
        <view
          class="size-item {{selectedSize === item.value ? 'selected' : ''}}"
          wx:for="{{sizeOptions}}"
          wx:key="value"
          bindtap="selectSize"
          data-size="{{item.value}}"
        >
          <view class="size-content">
            <view class="size-name">{{item.name}}</view>
            <view class="size-desc">{{item.width}}×{{item.height}}像素</view>
            <view class="size-mm">{{item.print_size}}</view>
          </view>
          <view wx:if="{{selectedSize === item.value}}" class="selected-indicator">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 处理状态提示 -->
  <view wx:if="{{processing}}" class="processing-section scale-in">
    <view class="processing-card glass-card">
      <view class="processing-spinner"></view>
      <text class="processing-text">正在处理照片，请稍候...</text>
      <text class="processing-subtitle">AI正在为您生成专业证件照</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="tips-section fade-in">
    <view class="tips-card glass-card">
      <view class="tips-header">
        <text class="tips-title">使用说明</text>
        <text class="tips-emoji">💡</text>
      </view>
      <view class="tips-list">
        <view class="tip-item">
          <text class="tip-icon">✓</text>
          <text class="tip-text">请选择清晰的正面照片</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">✓</text>
          <text class="tip-text">系统将自动进行人脸识别和抠图处理</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">✓</text>
          <text class="tip-text">支持多种背景色和尺寸规格</text>
        </view>
      </view>
    </view>
  </view>
</view>
