/**
 * 用户认证相关API接口
 * 对应服务端API文档中的用户认证模块 (/auth)
 */
const request = require('./request');

/**
 * 微信小程序用户登录
 * 对应接口: POST /auth/login
 * @param {string} code 微信登录code
 * @param {Object} userInfo 用户信息（可选）
 * @returns {Promise<Object>} 登录响应，包含access_token和用户信息
 */
function login(code, userInfo = null) {
  const requestData = {
    code
  };

  // 如果有用户信息，按照服务端格式添加
  if (userInfo) {
    requestData.user_info = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender || 0,
      country: userInfo.country || '',
      province: userInfo.province || '',
      city: userInfo.city || ''
    };
  }

  console.log('=== 用户登录API请求 ===');
  console.log('请求数据:', requestData);

  return request.post('/auth/login', requestData, {
    needAuth: false, // 登录接口不需要认证
    showLoading: false,
    showError: false
  });
}

/**
 * 验证token有效性（通过获取用户信息来验证）
 * 对应接口: GET /auth/user
 * @returns {Promise<Object>} 用户信息响应
 */
function validateToken() {
  console.log('=== 验证Token有效性API请求 ===');

  return request.get('/auth/user', {}, {
    showLoading: false,
    showError: false,
    needAuth: true
  });
}

/**
 * 获取当前登录用户的详细信息
 * 对应接口: GET /auth/user
 * @returns {Promise<Object>} 用户详细信息
 */
function getUserInfo() {
  console.log('=== 获取用户信息API请求 ===');

  return request.get('/auth/user', {}, {
    showLoading: false,
    needAuth: true
  });
}

/**
 * 更新当前用户的基本信息
 * 对应接口: PUT /auth/user
 * @param {Object} userInfo 用户信息
 * @param {string} userInfo.nickname 新昵称
 * @param {string} userInfo.avatar_url 新头像URL
 * @param {number} userInfo.gender 性别 (0-未知, 1-男, 2-女)
 * @param {string} userInfo.country 国家
 * @param {string} userInfo.province 省份
 * @param {string} userInfo.city 城市
 * @returns {Promise<Object>} 更新后的用户信息
 */
function updateUserInfo(userInfo) {
  console.log('=== 更新用户信息API请求 ===');
  console.log('更新数据:', userInfo);

  return request.put('/auth/user', userInfo, {
    showLoading: false,
    showError: false
  });
}

/**
 * 刷新当前用户的访问令牌
 * 对应接口: POST /auth/refresh
 * @returns {Promise<Object>} 新的token信息和用户信息
 */
function refreshToken() {
  console.log('=== 刷新访问令牌API请求 ===');

  return request.post('/auth/refresh', {}, {
    showLoading: false,
    showError: false,
    needAuth: true // 根据API文档，刷新token需要认证
  });
}

/**
 * 查询当前用户的会员状态
 * 对应接口: GET /auth/member-status
 * @returns {Promise<Object>} 会员状态信息
 */
function getMemberStatus() {
  console.log('=== 查询会员状态API请求 ===');

  return request.get('/auth/member-status', {}, {
    showLoading: false,
    showError: false,
    needAuth: true
  });
}

module.exports = {
  login,
  validateToken,
  getUserInfo,
  updateUserInfo,
  refreshToken,
  getMemberStatus
};
