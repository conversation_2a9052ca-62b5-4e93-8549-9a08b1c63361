# 微信小程序支付功能设计文档

## 概述

本设计文档描述了在现有微信小程序基础上集成支付功能的完整方案。系统将扩展现有的用户中心页面，添加会员管理、支付流程、订单管理等功能，并通过底部导航栏提供便捷的用户访问入口。

设计遵循现有项目的架构模式，使用相同的颜色方案（#4B8BF5主色调）和设计风格，确保用户体验的一致性。

## 架构

### 整体架构图

```mermaid
graph TB
    A[小程序入口] --> B[底部导航栏]
    B --> C[首页]
    B --> D[个人中心]
    
    D --> E[用户信息展示]
    D --> F[会员状态管理]
    D --> G[功能菜单]
    
    F --> H[会员套餐选择]
    H --> I[微信支付]
    I --> J[支付结果处理]
    J --> K[会员状态更新]
    
    G --> L[我的订单]
    G --> M[意见反馈]
    G --> N[客服微信]
    G --> O[常见问题]
    G --> P[设置]
    
    L --> Q[订单列表]
    Q --> R[订单详情]
    R --> S[重新支付]
```

### 技术架构

- **前端框架**: 微信小程序原生框架
- **状态管理**: 页面级状态管理 + 全局用户状态
- **网络请求**: 基于现有 `apiConfig.js` 的统一请求封装
- **支付集成**: 微信小程序支付 API
- **数据存储**: 微信小程序本地存储 + 服务端数据同步

## 组件和接口

### 页面组件结构

基于服务端接口设计，前端页面组件结构如下：

#### 1. 底部导航栏组件 (TabBar)
```
components/tabbar/
├── tabbar.wxml
├── tabbar.wxss
├── tabbar.js
└── tabbar.json
```

**功能特性:**
- 固定在页面底部
- 支持首页和个人中心切换
- 高亮显示当前选中状态
- 使用与现有设计一致的蓝色主题

#### 2. 扩展个人中心页面 (Enhanced User Center)
```
pages/user/center/
├── center.wxml (扩展现有)
├── center.wxss (扩展现有)
├── center.js (扩展现有)
└── center.json
```

**新增功能:**
- 会员权益状态展示（基于 `/api/member/benefits` 接口）
- PDF下载次数、证件照下载次数显示
- 会员到期时间提醒
- 续费/购买会员按钮
- 卡密兑换入口
- 我的订单入口

#### 3. 会员套餐选择页面
```
pages/membership/
├── packages/
│   ├── packages.wxml
│   ├── packages.wxss
│   ├── packages.js
│   └── packages.json
```

**功能特性:**
- 显示时长会员和次数会员套餐
- 套餐对比和推荐标识
- 支持不同产品类型选择（member_time/member_count）
- 集成支付流程

#### 4. 订单管理页面
```
pages/order/
├── list/
│   ├── list.wxml
│   ├── list.wxss
│   ├── list.js
│   └── list.json
└── detail/
    ├── detail.wxml
    ├── detail.wxss
    ├── detail.js
    └── detail.json
```

**功能特性:**
- 订单列表展示（基于 `/api/payment/query-order` 接口）
- 订单状态筛选（pending/paid/expired/refunded）
- 订单详情查看
- 重新支付功能（针对失败订单）

#### 5. 卡密兑换页面
```
pages/card/
├── redeem/
│   ├── redeem.wxml
│   ├── redeem.wxss
│   ├── redeem.js
│   └── redeem.json
```

**功能特性:**
- 卡密输入和验证（基于 `/api/card/check` 接口）
- 卡密兑换处理（基于 `/api/card/redeem` 接口）
- 兑换结果展示
- 权益激活确认

#### 6. 支付结果页面
```
pages/payment/
├── result/
│   ├── result.wxml
│   ├── result.wxss
│   ├── result.js
│   └── result.json
```

**功能特性:**
- 支付成功/失败结果展示
- 权益激活状态显示
- 返回个人中心或重新支付选项

### API接口设计

根据服务端设计文档，前端需要对接以下接口：

#### 支付相关接口

1. **创建支付订单**
```javascript
// 创建支付订单
POST /api/payment/create-order
Request: {
  "product_id": "string",           // 产品ID
  "product_type": "member_time|member_count", // 产品类型：时长会员或次数会员
  "amount": "number",               // 支付金额
  "description": "string",          // 订单描述
  "source": "string"                // 来源标识
}
Response: {
  "order_id": "string",
  "payment_params": {
    "appId": "string",
    "timeStamp": "string",
    "nonceStr": "string",
    "package": "string",
    "signType": "string",
    "paySign": "string"
  }
}
```

2. **查询订单状态**
```javascript
// 查询订单状态
GET /api/payment/query-order/{order_id}
Response: {
  "order_id": "string",
  "status": "pending|paid|expired|refunded",
  "created_at": "datetime",
  "paid_at": "datetime|null",
  "amount": "number",
  "description": "string"
}
```

#### 会员权益相关接口

1. **查询会员权益**
```javascript
// 查询用户会员权益
GET /api/member/benefits
Response: {
  "is_member": "boolean",
  "member_type": "string|null",
  "expire_at": "datetime|null",
  "benefits": {
    "pdf_download_count": "number",      // PDF下载次数
    "photo_download_count": "number",    // 证件照下载次数
    "premium_templates_access": "boolean" // 高级模板访问权限
  }
}
```

2. **消耗权益**
```javascript
// 消耗用户权益
POST /api/member/consume-benefit
Request: {
  "benefit_type": "pdf_download|photo_download|template_access",
  "resource_id": "string"              // 资源ID（可选）
}
Response: {
  "success": "boolean",
  "remaining": "number",               // 剩余次数
  "message": "string"
}
```

#### 卡密相关接口

1. **兑换卡密**
```javascript
// 兑换卡密
POST /api/card/redeem
Request: {
  "card_code": "string"
}
Response: {
  "success": "boolean",
  "benefit_type": "member_time|pdf_count|photo_count",
  "benefit_value": "number",
  "message": "string"
}
```

2. **查询卡密状态**
```javascript
// 查询卡密状态（兑换前验证）
POST /api/card/check
Request: {
  "card_code": "string"
}
Response: {
  "valid": "boolean",
  "used": "boolean",
  "benefit_type": "string",
  "benefit_value": "number"
}
```

## 数据模型

### 前端数据模型

基于服务端数据结构，前端需要定义以下数据模型：

#### 用户会员信息模型
```javascript
const MembershipInfo = {
  is_member: Boolean,           // 是否为会员
  member_type: String,          // 会员类型: null, "monthly", "yearly", "lifetime"
  expire_at: Date,              // 到期时间
  benefits: {                   // 权益详情
    pdf_download_count: Number,      // PDF下载剩余次数
    photo_download_count: Number,    // 证件照下载剩余次数
    premium_templates_access: Boolean // 高级模板访问权限
  }
}
```

#### 订单信息模型
```javascript
const OrderInfo = {
  order_id: String,             // 订单ID
  order_no: String,             // 订单号
  product_id: String,           // 产品ID
  product_type: String,         // 产品类型: "member_time", "member_count"
  amount: Number,               // 支付金额
  description: String,          // 订单描述
  status: String,               // 订单状态: "pending", "paid", "expired", "refunded"
  created_at: Date,             // 创建时间
  paid_at: Date,                // 支付时间
  source: String                // 订单来源
}
```

#### 会员套餐模型
```javascript
const MembershipPackage = {
  product_id: String,           // 产品ID
  name: String,                 // 套餐名称
  product_type: String,         // 产品类型: "member_time", "member_count"
  price: Number,                // 价格
  duration: Number,             // 有效期（天数，仅时长会员）
  count: Number,                // 次数（仅次数会员）
  description: String,          // 套餐描述
  benefits: Array,              // 权益列表
  is_popular: Boolean,          // 是否热门推荐
  original_price: Number        // 原价（用于显示折扣）
}
```

#### 卡密信息模型
```javascript
const CardInfo = {
  card_code: String,            // 卡密代码
  valid: Boolean,               // 是否有效
  used: Boolean,                // 是否已使用
  benefit_type: String,         // 权益类型: "member_time", "pdf_count", "photo_count"
  benefit_value: Number,        // 权益数值
  expire_at: Date               // 卡密过期时间
}
```

#### 权益消耗记录模型
```javascript
const BenefitConsumption = {
  id: String,                   // 记录ID
  benefit_type: String,         // 权益类型
  consumed_value: Number,       // 消耗数量
  resource_id: String,          // 资源ID
  created_at: Date,             // 消耗时间
  status: String,               // 状态: "success", "failed", "refunded"
  remark: String                // 备注
}
```

## 错误处理

### 支付错误处理策略

1. **网络错误**
   - 显示友好的错误提示
   - 提供重试按钮
   - 记录错误日志

2. **支付取消**
   - 返回套餐选择页面
   - 保留用户选择状态
   - 不显示错误信息

3. **支付失败**
   - 显示具体失败原因
   - 提供重新支付选项
   - 联系客服入口

4. **接口超时**
   - 使用现有的超时配置
   - 实现重试机制
   - 降级处理方案

### 错误处理和状态码

根据服务端设计，前端需要处理以下错误情况：

#### 错误响应格式
```javascript
// 服务端统一错误响应格式
const ErrorResponse = {
  success: false,
  error_code: "string",
  message: "string",
  details: "object|null",
  request_id: "string"
}
```

#### 前端错误码定义
```javascript
const ErrorCodes = {
  // 支付相关错误
  PAYMENT_CANCELLED: 'PAYMENT_CANCELLED',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  ORDER_EXPIRED: 'ORDER_EXPIRED',
  INVALID_ORDER: 'INVALID_ORDER',
  
  // 权益相关错误
  BENEFIT_INSUFFICIENT: 'BENEFIT_INSUFFICIENT',
  BENEFIT_EXPIRED: 'BENEFIT_EXPIRED',
  INVALID_BENEFIT_TYPE: 'INVALID_BENEFIT_TYPE',
  
  // 卡密相关错误
  CARD_INVALID: 'CARD_INVALID',
  CARD_USED: 'CARD_USED',
  CARD_EXPIRED: 'CARD_EXPIRED',
  
  // 系统错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  USER_NOT_LOGIN: 'USER_NOT_LOGIN',
  PERMISSION_DENIED: 'PERMISSION_DENIED'
}
```

#### 错误处理策略
```javascript
const ErrorHandlers = {
  // 支付错误处理
  handlePaymentError: (errorCode, message) => {
    switch(errorCode) {
      case 'PAYMENT_CANCELLED':
        // 用户取消支付，返回套餐选择页面
        break;
      case 'PAYMENT_FAILED':
        // 支付失败，显示错误信息和重试选项
        break;
      case 'ORDER_EXPIRED':
        // 订单过期，重新创建订单
        break;
    }
  },
  
  // 权益错误处理
  handleBenefitError: (errorCode, message) => {
    switch(errorCode) {
      case 'BENEFIT_INSUFFICIENT':
        // 权益不足，引导用户购买或兑换
        break;
      case 'BENEFIT_EXPIRED':
        // 权益过期，提示续费
        break;
    }
  },
  
  // 卡密错误处理
  handleCardError: (errorCode, message) => {
    switch(errorCode) {
      case 'CARD_INVALID':
        // 卡密无效，提示重新输入
        break;
      case 'CARD_USED':
        // 卡密已使用，显示相应提示
        break;
    }
  }
}
```

## 测试策略

### 单元测试
- 支付流程核心逻辑测试
- 会员状态管理测试
- 数据模型验证测试
- API接口调用测试

### 集成测试
- 支付流程端到端测试
- 会员状态同步测试
- 订单状态更新测试
- 错误处理流程测试

### 用户体验测试
- 支付流程用户体验测试
- 页面加载性能测试
- 不同网络环境下的稳定性测试
- 多设备兼容性测试

### 测试用例覆盖
1. **正常支付流程**
   - 选择套餐 → 发起支付 → 支付成功 → 会员状态更新
   
2. **异常支付流程**
   - 支付取消、支付失败、网络异常等场景
   
3. **会员状态管理**
   - 会员到期提醒、状态刷新、特权验证
   
4. **订单管理**
   - 订单列表展示、订单详情查看、重新支付

## UI设计规范

### 颜色方案
- **主色调**: #4B8BF5 (与现有导航栏保持一致)
- **渐变背景**: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%)
- **会员金色**: #FFD700 (会员标识)
- **成功绿色**: #52C41A (支付成功)
- **错误红色**: #FF4D4F (支付失败)
- **文字颜色**: #333333 (主要文字), #666666 (次要文字), #999999 (辅助文字)

### 组件设计规范

#### 会员卡片设计
```css
.membership-card {
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(75, 139, 245, 0.3);
}
```

#### 按钮设计规范
```css
.primary-button {
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  border-radius: 50rpx;
  color: white;
  font-size: 32rpx;
  padding: 24rpx 48rpx;
}

.secondary-button {
  background: white;
  border: 2rpx solid #4B8BF5;
  color: #4B8BF5;
  border-radius: 50rpx;
}
```

#### 卡片容器设计
```css
.card-container {
  background: white;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
```

### 响应式设计
- 支持不同屏幕尺寸的适配
- 使用rpx单位确保在不同设备上的一致性
- 考虑iPhone X系列的安全区域适配

### 动画效果
- 页面切换使用淡入淡出效果
- 按钮点击提供触觉反馈
- 支付成功使用庆祝动画
- 加载状态使用统一的loading组件

## 网络请求封装

### API配置扩展

基于现有的 `config/apiConfig.js`，需要添加支付相关的API配置：

```javascript
// 在 commonConfig 中添加支付相关的API路径
const paymentApiConfig = {
  // 支付相关接口
  createOrderUrl: '/api/payment/create-order',
  queryOrderUrl: '/api/payment/query-order',
  paymentNotifyUrl: '/api/payment/notify',
  
  // 会员权益接口
  memberBenefitsUrl: '/api/member/benefits',
  consumeBenefitUrl: '/api/member/consume-benefit',
  
  // 卡密相关接口
  cardRedeemUrl: '/api/card/redeem',
  cardCheckUrl: '/api/card/check',
  
  // 超时配置
  paymentTimeout: 30000,        // 支付请求超时时间（30秒）
  queryTimeout: 10000,          // 查询请求超时时间（10秒）
}
```

### 请求封装服务

```javascript
// utils/paymentApi.js
const apiConfig = require('../config/apiConfig');

class PaymentAPI {
  // 创建支付订单
  static async createOrder(orderData) {
    return await this.request({
      url: apiConfig.baseUrl + apiConfig.createOrderUrl,
      method: 'POST',
      data: orderData,
      timeout: apiConfig.paymentTimeout
    });
  }
  
  // 查询订单状态
  static async queryOrder(orderId) {
    return await this.request({
      url: `${apiConfig.baseUrl}${apiConfig.queryOrderUrl}/${orderId}`,
      method: 'GET',
      timeout: apiConfig.queryTimeout
    });
  }
  
  // 查询会员权益
  static async getMemberBenefits() {
    return await this.request({
      url: apiConfig.baseUrl + apiConfig.memberBenefitsUrl,
      method: 'GET'
    });
  }
  
  // 消耗权益
  static async consumeBenefit(benefitData) {
    return await this.request({
      url: apiConfig.baseUrl + apiConfig.consumeBenefitUrl,
      method: 'POST',
      data: benefitData
    });
  }
  
  // 兑换卡密
  static async redeemCard(cardCode) {
    return await this.request({
      url: apiConfig.baseUrl + apiConfig.cardRedeemUrl,
      method: 'POST',
      data: { card_code: cardCode }
    });
  }
  
  // 检查卡密
  static async checkCard(cardCode) {
    return await this.request({
      url: apiConfig.baseUrl + apiConfig.cardCheckUrl,
      method: 'POST',
      data: { card_code: cardCode }
    });
  }
  
  // 统一请求方法
  static async request(options) {
    return new Promise((resolve, reject) => {
      // 添加认证头
      const header = {
        'Content-Type': 'application/json',
        ...this.getAuthHeader()
      };
      
      wx.request({
        ...options,
        header,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.success !== false) {
              resolve(res.data);
            } else {
              reject(new Error(res.data.message || '请求失败'));
            }
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data.message || '网络错误'}`));
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || '网络请求失败'));
        }
      });
    });
  }
  
  // 获取认证头
  static getAuthHeader() {
    const token = wx.getStorageSync('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }
}

module.exports = PaymentAPI;
```

## 性能优化

### 页面加载优化
- 使用小程序的分包加载机制，将支付相关页面放入独立分包
- 图片资源使用CDN加速
- 关键路径优先加载，非关键功能延迟加载

### 网络请求优化
- 复用现有的请求封装和缓存机制
- 实现请求去重和防抖，避免重复请求
- 支付状态轮询优化，使用指数退避算法
- 会员权益信息缓存，减少频繁查询

### 内存管理
- 及时清理页面数据，特别是订单列表和支付参数
- 避免内存泄漏，正确处理定时器和事件监听
- 合理使用缓存策略，平衡性能和内存使用

### 支付流程优化
```javascript
// 支付状态轮询优化
class PaymentStatusPoller {
  constructor(orderId, callback) {
    this.orderId = orderId;
    this.callback = callback;
    this.retryCount = 0;
    this.maxRetries = 10;
    this.baseDelay = 1000; // 1秒
  }
  
  start() {
    this.poll();
  }
  
  async poll() {
    try {
      const result = await PaymentAPI.queryOrder(this.orderId);
      
      if (result.status === 'paid') {
        this.callback({ success: true, data: result });
        return;
      }
      
      if (result.status === 'expired' || result.status === 'refunded') {
        this.callback({ success: false, data: result });
        return;
      }
      
      // 继续轮询
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        const delay = this.baseDelay * Math.pow(2, this.retryCount - 1);
        setTimeout(() => this.poll(), Math.min(delay, 10000));
      } else {
        this.callback({ success: false, error: '查询超时' });
      }
    } catch (error) {
      this.callback({ success: false, error: error.message });
    }
  }
  
  stop() {
    this.retryCount = this.maxRetries;
  }
}
```