const app = getApp();

Page({
  data: {
    status: 'success', // success, failed, cancelled
    orderInfo: {},
    membershipInfo: '',
    failReason: ''
  },

  onLoad(options) {
    const { status, orderId, reason } = options;
    
    this.setData({
      status: status || 'success',
      failReason: reason || ''
    });

    if (status === 'success' && orderId) {
      this.loadOrderInfo(orderId);
    }
  },

  // 加载订单信息
  async loadOrderInfo(orderId) {
    try {
      const paymentApi = require('../../../utils/api/paymentApi.js');
      const result = await paymentApi.queryOrder(orderId);
      
      if (result.success && result.data) {
        const orderInfo = result.data;
        let membershipInfo = '';
        
        if (orderInfo.plan_id === 'permanent') {
          membershipInfo = '永久有效';
        } else {
          const expireDate = new Date(orderInfo.expire_time);
          membershipInfo = `${expireDate.getFullYear()}-${(expireDate.getMonth() + 1).toString().padStart(2, '0')}-${expireDate.getDate().toString().padStart(2, '0')}`;
        }

        this.setData({
          orderInfo,
          membershipInfo
        });
      }
    } catch (error) {
      console.error('加载订单信息失败:', error);
    }
  },

  // 前往用户中心
  goToUserCenter() {
    wx.redirectTo({
      url: '/pages/user/center/center'
    });
  },

  // 重新支付
  retryPayment() {
    wx.redirectTo({
      url: '/pages/payment/membership/membership'
    });
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});