.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px; /* 增加高度以容纳更大的图标和文本 */
  background: var(--bg-primary);
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: var(--shadow-lg);
  border-top: 1rpx solid var(--border-color);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  transition: all 0.2s ease-in-out;
}

.tab-bar-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.tab-bar-icon {
  width: 24px; /* 稍微减小图标尺寸 */
  height: 24px;
  margin-bottom: 4px; /* 增加图标和文本之间的间距 */
}

.tab-bar-text {
  font-size: 12px; /* 增大字体大小 */
}
