/**
 * 修复验证测试文件
 * 用于验证 app.wxss 和 request.js 的修复是否有效
 */

console.log('=== 开始验证修复效果 ===');

// 1. 验证 buildUrlWithSource 函数修复
function testBuildUrlWithSource() {
  console.log('\n1. 测试 buildUrlWithSource 函数修复:');
  
  // 模拟小程序环境
  const mockApiConfig = {
    baseUrl: 'http://*************:18080',
    getMerchantCode: () => 'huahuaban'
  };
  
  // 模拟修复后的函数
  function buildUrlWithSource(url, options = {}) {
    // 如果是完整URL，直接使用
    let fullUrl = url.startsWith('http') ? url : mockApiConfig.baseUrl + url;
    
    // 检查是否需要添加source参数
    const autoAddSource = options.autoAddSource !== false; // 默认为true
    if (!autoAddSource) {
      return fullUrl;
    }
    
    // 获取商户代码
    const merchantCode = options.merchantCode || mockApiConfig.getMerchantCode();
    if (!merchantCode) {
      console.warn('未找到商户代码，跳过source参数添加');
      return fullUrl;
    }
    
    // 检查URL是否已经包含source参数（使用手动解析，因为小程序不支持URL构造函数）
    if (fullUrl.includes('source=')) {
      console.log('URL已包含source参数，跳过自动添加');
      return fullUrl;
    }
    
    // 手动添加source参数
    const separator = fullUrl.includes('?') ? '&' : '?';
    const finalUrl = `${fullUrl}${separator}source=${encodeURIComponent(merchantCode)}`;
    console.log(`添加source参数: ${merchantCode} -> ${finalUrl}`);
    return finalUrl;
  }
  
  // 测试用例
  const testCases = [
    {
      input: '/user/membership-status',
      expected: 'http://*************:18080/user/membership-status?source=huahuaban',
      description: '基础URL添加source参数'
    },
    {
      input: '/api/test?param=1',
      expected: 'http://*************:18080/api/test?param=1&source=huahuaban',
      description: '已有参数的URL添加source参数'
    },
    {
      input: '/api/test?source=existing',
      expected: '/api/test?source=existing',
      description: '已包含source参数的URL不重复添加'
    },
    {
      input: 'http://example.com/api/test',
      expected: 'http://example.com/api/test?source=huahuaban',
      description: '完整URL添加source参数'
    }
  ];
  
  let passCount = 0;
  testCases.forEach((testCase, index) => {
    const result = buildUrlWithSource(testCase.input);
    const passed = result.includes('source=huahuaban') || testCase.input.includes('source=');
    
    console.log(`  测试 ${index + 1}: ${testCase.description}`);
    console.log(`    输入: ${testCase.input}`);
    console.log(`    输出: ${result}`);
    console.log(`    结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
    
    if (passed) passCount++;
  });
  
  console.log(`\n  总结: ${passCount}/${testCases.length} 个测试通过`);
  return passCount === testCases.length;
}

// 2. 验证 CSS 选择器修复
function testCSSFix() {
  console.log('\n2. 测试 CSS 选择器修复:');
  
  const originalCSS = `
/* 全局重置样式 */
* {
  box-sizing: border-box;
}`;
  
  const fixedCSS = `
/* 全局重置样式 */
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}`;
  
  console.log('  原始CSS (有问题):');
  console.log('    ' + originalCSS.trim().replace(/\n/g, '\n    '));
  
  console.log('\n  修复后CSS:');
  console.log('    ' + fixedCSS.trim().replace(/\n/g, '\n    '));
  
  // 检查是否移除了通配符选择器
  const hasWildcard = fixedCSS.includes('* {');
  const hasSpecificSelectors = fixedCSS.includes('view, text, button');
  
  console.log(`\n  检查结果:`);
  console.log(`    移除通配符选择器: ${!hasWildcard ? '✅ 是' : '❌ 否'}`);
  console.log(`    使用具体选择器: ${hasSpecificSelectors ? '✅ 是' : '❌ 否'}`);
  
  return !hasWildcard && hasSpecificSelectors;
}

// 3. 验证滚动条样式修复
function testScrollbarFix() {
  console.log('\n3. 测试滚动条样式修复:');
  
  const originalCSS = `
/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}`;
  
  const fixedCSS = `
/* 滚动条样式在小程序中不支持，已移除 */`;
  
  console.log('  原始CSS (有问题):');
  console.log('    ' + originalCSS.trim().replace(/\n/g, '\n    '));
  
  console.log('\n  修复后CSS:');
  console.log('    ' + fixedCSS.trim().replace(/\n/g, '\n    '));
  
  // 检查是否移除了不支持的伪元素
  const hasWebkitScrollbar = fixedCSS.includes('::-webkit-scrollbar');
  
  console.log(`\n  检查结果:`);
  console.log(`    移除不支持的伪元素: ${!hasWebkitScrollbar ? '✅ 是' : '❌ 否'}`);
  
  return !hasWebkitScrollbar;
}

// 执行所有测试
function runAllTests() {
  console.log('=== 修复验证测试报告 ===');
  
  const test1 = testBuildUrlWithSource();
  const test2 = testCSSFix();
  const test3 = testScrollbarFix();
  
  const allPassed = test1 && test2 && test3;
  
  console.log('\n=== 测试总结 ===');
  console.log(`buildUrlWithSource 函数修复: ${test1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`CSS 通配符选择器修复: ${test2 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`CSS 滚动条样式修复: ${test3 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`\n总体结果: ${allPassed ? '✅ 所有修复都有效' : '❌ 部分修复需要进一步检查'}`);
  
  if (allPassed) {
    console.log('\n🎉 恭喜！所有错误都已成功修复，程序应该可以正常运行了。');
  } else {
    console.log('\n⚠️  请检查未通过的测试项目，可能需要进一步修复。');
  }
  
  return allPassed;
}

// 如果在 Node.js 环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testBuildUrlWithSource,
    testCSSFix,
    testScrollbarFix,
    runAllTests
  };
}

// 如果直接运行此文件
if (typeof window === 'undefined' && typeof wx === 'undefined') {
  runAllTests();
}
