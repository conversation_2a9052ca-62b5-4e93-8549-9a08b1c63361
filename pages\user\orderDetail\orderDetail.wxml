<view class="container">
  <!-- 订单状态 -->
  <view class="status-section">
    <image class="status-icon" src="/pages/user/orderDetail/images/{{orderInfo.status}}.png" mode="aspectFit"></image>
    <text class="status-text">{{orderInfo.status_text}}</text>
    <text wx:if="{{orderInfo.status === 'pending'}}" class="status-desc">请在{{expireTime}}前完成支付</text>
  </view>

  <!-- 商品信息 -->
  <view class="product-section">
    <text class="section-title">商品信息</text>
    <view class="product-item">
      <image class="product-icon" src="/pages/user/orderDetail/images/membership.png" mode="aspectFit"></image>
      <view class="product-info">
        <text class="product-name">{{orderInfo.product_name}}</text>
        <text class="product-duration">{{orderInfo.duration}}</text>
      </view>
      <text class="product-price">¥{{orderInfo.amount}}</text>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-section">
    <text class="section-title">订单信息</text>
    <view class="order-details">
      <view class="detail-row">
        <text class="detail-label">订单号</text>
        <text class="detail-value" bindtap="copyOrderNo">{{orderInfo.order_no}}</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">创建时间</text>
        <text class="detail-value">{{orderInfo.create_time}}</text>
      </view>
      <view wx:if="{{orderInfo.pay_time}}" class="detail-row">
        <text class="detail-label">支付时间</text>
        <text class="detail-value">{{orderInfo.pay_time}}</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">支付方式</text>
        <text class="detail-value">微信支付</text>
      </view>
    </view>
  </view>

  <!-- 金额明细 -->
  <view class="amount-section">
    <text class="section-title">金额明细</text>
    <view class="amount-details">
      <view class="amount-row">
        <text class="amount-label">商品金额</text>
        <text class="amount-value">¥{{orderInfo.amount}}</text>
      </view>
      <view class="amount-row">
        <text class="amount-label">优惠金额</text>
        <text class="amount-value">-¥{{orderInfo.discount || '0.00'}}</text>
      </view>
      <view class="amount-row total">
        <text class="amount-label">实付金额</text>
        <text class="amount-value">¥{{orderInfo.amount}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button wx:if="{{orderInfo.status === 'pending'}}" class="pay-btn" bindtap="repay">
      立即支付
    </button>
    <button wx:if="{{orderInfo.status === 'pending'}}" class="cancel-btn" bindtap="cancelOrder">
      取消订单
    </button>
    <button class="contact-btn" bindtap="contactService">
      联系客服
    </button>
  </view>
</view>