.container {
  min-height: 100vh;
  background: var(--primary-gradient);
  padding: var(--space-xl) var(--space-md) 0;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 180rpx;
  height: 180rpx;
  top: 15%;
  right: -40rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 65%;
  left: -30rpx;
  animation-delay: 3s;
}

/* 页面标题 */
.page-header {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.header-card {
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.header-icon {
  font-size: var(--text-3xl);
  opacity: 0.8;
}



/* 加载状态 */
.loading-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.loading-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl) var(--space-xl);
  text-align: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 500;
}

/* 尺寸选择区域 */
.size-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.size-card {
  padding: var(--space-xl);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.section-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.size-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.size-grid .size-item {
  flex: 0 0 48%;
  margin-bottom: var(--space-md);
}

.size-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.size-item:active {
  transform: scale(0.98);
}

.size-item.selected {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: var(--shadow-lg);
}

.size-content {
  position: relative;
  z-index: 1;
}

.size-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.size-item.selected .size-name {
  color: var(--text-white);
}

.size-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.size-item.selected .size-desc {
  color: rgba(255, 255, 255, 0.9);
}

.size-mm {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.size-item.selected .size-mm {
  color: rgba(255, 255, 255, 0.8);
}

.selected-indicator {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  color: var(--primary-color);
  font-weight: 600;
}



/* 处理状态 */
.processing-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.processing-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl) var(--space-xl);
  text-align: center;
}

.processing-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-lg);
}

.processing-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--space-sm);
}

.processing-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 使用说明 */
.tips-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.tips-card {
  padding: var(--space-xl);
}

.tips-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
}

.tips-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.tips-emoji {
  font-size: var(--text-xl);
  opacity: 0.8;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.tip-item {
  display: flex;
  align-items: center;
  padding: var(--space-sm) 0;
}

.tip-icon {
  color: var(--success-color);
  font-size: var(--text-sm);
  font-weight: 600;
  margin-right: var(--space-sm);
  width: 24rpx;
  text-align: center;
}

.tip-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  flex: 1;
}
