.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
}

.feedback-form {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.form-section {
  margin-bottom: 40rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

/* 反馈类型 */
.feedback-types {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.type-item {
  padding: 15rpx 25rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.type-item.selected {
  border-color: #4B8BF5;
  background: #4B8BF5;
  color: white;
}

/* 问题描述 */
.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.feedback-textarea:focus {
  border-color: #4B8BF5;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  display: block;
  margin-top: 10rpx;
}

/* 联系方式 */
.contact-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.contact-input:focus {
  border-color: #4B8BF5;
}

/* 提交按钮 */
.submit-btn {
  background: #4B8BF5;
  color: white;
  border: none;
  border-radius: 12rpx;
  height: 80rpx;
  font-size: 30rpx;
  font-weight: 600;
  margin-top: 40rpx;
}

.submit-btn:disabled {
  background: #ccc;
}

/* 联系信息 */
.contact-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.contact-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
}

.contact-value {
  font-size: 26rpx;
  color: #4B8BF5;
  font-weight: 600;
}