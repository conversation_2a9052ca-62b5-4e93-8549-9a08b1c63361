/**
 * 会员管理相关API接口
 * 对应服务端API文档中的会员管理模块 (/membership)
 */
const request = require('./request.js');

/**
 * 会员管理API类
 */
class MembershipApi {

  /**
   * 获取当前用户的会员状态和信息
   * 对应接口: GET /membership/status
   * @returns {Promise<Object>} 会员状态响应
   */
  async getMembershipStatus() {
    try {
      console.log('=== 获取会员状态API请求 ===');
      
      const response = await request.get('/membership/status', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取会员状态失败:', error);
      return { success: false, message: '获取会员状态失败' };
    }
  }

  /**
   * 获取当前用户的会员权益信息
   * 对应接口: GET /membership/benefits
   * @returns {Promise<Object>} 会员权益响应
   */
  async getMembershipBenefits() {
    try {
      console.log('=== 获取会员权益API请求 ===');
      
      const response = await request.get('/membership/benefits', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取会员权益失败:', error);
      return { success: false, message: '获取会员权益失败' };
    }
  }

  /**
   * 检查当前用户是否有权限使用指定功能
   * 对应接口: GET /membership/check-permission/{feature}
   * @param {string} feature 功能名称，如resume_export, idphoto_generate, premium_templates
   * @returns {Promise<Object>} 权限检查响应
   */
  async checkFeaturePermission(feature) {
    try {
      console.log('=== 检查功能权限API请求 ===');
      console.log('功能名称:', feature);
      
      const response = await request.get(`/membership/check-permission/${feature}`, {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('检查功能权限失败:', error);
      return { success: false, message: '检查功能权限失败' };
    }
  }

  /**
   * 同步用户会员状态（检查过期等）
   * 对应接口: POST /membership/sync-status
   * @returns {Promise<Object>} 同步结果响应
   */
  async syncMembershipStatus() {
    try {
      console.log('=== 同步会员状态API请求 ===');
      
      const response = await request.post('/membership/sync-status', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('同步会员状态失败:', error);
      return { success: false, message: '同步会员状态失败' };
    }
  }

  /**
   * 获取当前用户的功能使用统计
   * 对应接口: GET /membership/usage-stats
   * @returns {Promise<Object>} 使用统计响应
   */
  async getUsageStats() {
    try {
      console.log('=== 获取使用统计API请求 ===');
      
      const response = await request.get('/membership/usage-stats', {}, {
        showLoading: false,
        showError: false,
        needAuth: true
      });
      return response;
    } catch (error) {
      console.error('获取使用统计失败:', error);
      return { success: false, message: '获取使用统计失败' };
    }
  }

  /**
   * 批量检查多个功能的权限
   * @param {Array<string>} features 功能名称数组
   * @returns {Promise<Object>} 批量权限检查结果
   */
  async checkMultiplePermissions(features) {
    try {
      console.log('=== 批量检查功能权限 ===');
      console.log('功能列表:', features);
      
      const results = {};
      
      // 并发检查所有功能权限
      const promises = features.map(async (feature) => {
        try {
          const result = await this.checkFeaturePermission(feature);
          return { feature, result };
        } catch (error) {
          return { feature, result: { success: false, message: '检查失败' } };
        }
      });
      
      const responses = await Promise.all(promises);
      
      // 整理结果
      responses.forEach(({ feature, result }) => {
        results[feature] = result;
      });
      
      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('批量检查功能权限失败:', error);
      return { success: false, message: '批量检查功能权限失败' };
    }
  }

  /**
   * 获取会员状态摘要信息（简化版本）
   * @returns {Promise<Object>} 会员状态摘要
   */
  async getMembershipSummary() {
    try {
      console.log('=== 获取会员状态摘要 ===');
      
      const statusResult = await this.getMembershipStatus();
      
      if (statusResult.success !== false && statusResult.is_member !== undefined) {
        return {
          success: true,
          is_member: statusResult.is_member,
          current_membership: statusResult.current_membership || null,
          message: statusResult.is_member ? '会员用户' : '普通用户'
        };
      } else {
        return {
          success: false,
          is_member: false,
          message: '获取会员状态失败'
        };
      }
    } catch (error) {
      console.error('获取会员状态摘要失败:', error);
      return {
        success: false,
        is_member: false,
        message: '获取会员状态摘要失败'
      };
    }
  }
}

module.exports = new MembershipApi();
