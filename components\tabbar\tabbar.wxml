<view class="tabbar-container">
  <view class="tabbar-item {{currentTab === 'home' ? 'active' : ''}}" bindtap="switchTab" data-tab="home">
    <image class="tabbar-icon" src="{{currentTab === 'home' ? '/components/tabbar/images/home-active.png' : '/components/tabbar/images/home.png'}}" mode="aspectFit"></image>
    <text class="tabbar-text">首页</text>
  </view>
  <view class="tabbar-item {{currentTab === 'user' ? 'active' : ''}}" bindtap="switchTab" data-tab="user">
    <image class="tabbar-icon" src="{{currentTab === 'user' ? '/components/tabbar/images/user-active.png' : '/components/tabbar/images/user.png'}}" mode="aspectFit"></image>
    <text class="tabbar-text">个人中心</text>
  </view>
</view>