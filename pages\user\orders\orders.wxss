.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-number {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.order-status.pending {
  background: #FFF3E0;
  color: #FF9800;
}

.order-status.paid {
  background: #E8F5E8;
  color: #4CAF50;
}

.order-status.cancelled {
  background: #FFEBEE;
  color: #F44336;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.product-duration {
  font-size: 24rpx;
  color: #666;
}

.order-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #4B8BF5;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.pay-btn {
  background: #4B8BF5;
  color: white;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.go-buy-btn {
  background: #4B8BF5;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}