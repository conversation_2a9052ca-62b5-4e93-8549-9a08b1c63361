## 6. 证件照生成模块 (/idphoto)

### 6.1 健康检查
**接口**: `GET /idphoto/health`
**描述**: 检查证件照服务的健康状态
**认证**: 不需要

**响应示例**:
```json
{
  "status": "ok",
  "message": "证件照服务运行正常"
}
```

### 6.2 生成证件照
**接口**: `POST /idphoto/generate`
**描述**: 上传照片并生成指定尺寸和背景色的证件照
**认证**: 需要

**请求参数**:
- `image` (file): 上传的照片文件 (必填)
- `size` (string): 证件照尺寸，可选值：one_inch、two_inch、big_one_inch、small_one_inch、big_two_inch、small_two_inch (默认：one_inch)
- `color` (string): 背景颜色，可选值：transparent、white、blue、red、blue_gradient、red_gradient (默认：white)

**响应示例**:
```json
{
  "success": true,
  "message": "证件照生成成功",
  "data": {
    "image_base64": "base64编码的证件照图片",
    "size": "one_inch",
    "size_name": "一寸",
    "color": "white",
    "color_name": "白色",
    "dimensions": {
      "width": 295,
      "height": 413
    },
    "hd_image_base64": "base64编码的高清证件照图片",
    "transparent_base64": "base64编码的透明背景证件照图片"
  }
}
```

### 6.3 获取支持的尺寸列表
**接口**: `GET /idphoto/sizes`
**描述**: 获取所有支持的证件照尺寸规格
**认证**: 不需要

**响应示例**:
```json
{
  "success": true,
  "message": "获取尺寸列表成功",
  "data": {
    "sizes": [
      {
        "name": "一寸",
        "value": "one_inch",
        "width": 295,
        "height": 413,
        "print_size": "2.5cm*3.5cm",
        "description": "标准一寸证件照"
      },
      {
        "name": "二寸",
        "value": "two_inch",
        "width": 413,
        "height": 579,
        "print_size": "3.5cm*4.9cm",
        "description": "标准二寸证件照"
      }
    ]
  }
}
```

### 6.4 获取支持的颜色列表
**接口**: `GET /idphoto/colors`
**描述**: 获取所有支持的背景颜色选项
**认证**: 不需要

**响应示例**:
```json
{
  "success": true,
  "message": "获取颜色列表成功",
  "data": {
    "colors": [
      {
        "name": "白色",
        "value": "white",
        "hex": "FFFFFF",
        "render": 0,
        "description": "白色背景"
      },
      {
        "name": "蓝色渐变",
        "value": "blue_gradient",
        "hex": "438EDB",
        "render": 1,
        "description": "蓝色渐变背景"
      }
    ]
  }
}
```

---

## 证件照API使用示例

### 微信小程序端调用示例

```javascript
// 选择图片并生成证件照
wx.chooseImage({
  count: 1,
  sizeType: ['original'],
  sourceType: ['album', 'camera'],
  success: (res) => {
    const tempFilePath = res.tempFilePaths[0];

    // 上传并生成证件照
    wx.uploadFile({
      url: 'https://your-domain.com/idphoto/generate',
      filePath: tempFilePath,
      name: 'image',
      formData: {
        'size': 'one_inch',
        'color': 'white'
      },
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
      },
      success: (uploadRes) => {
        const data = JSON.parse(uploadRes.data);
        if (data.success) {
          console.log('证件照生成成功');

          // 显示生成的证件照
          const imageBase64 = data.data.image_base64;
          const imageSrc = 'data:image/jpeg;base64,' + imageBase64;

          // 更新页面显示
          this.setData({
            idPhotoSrc: imageSrc
          });
        } else {
          wx.showToast({
            title: '生成失败',
            icon: 'error'
          });
        }
      },
      fail: (err) => {
        console.error('上传失败:', err);
        wx.showToast({
          title: '上传失败',
          icon: 'error'
        });
      }
    });
  }
});
```

### cURL调用示例

```bash
# 生成证件照
curl -X POST "http://localhost:18080/idphoto/generate" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "image=@/path/to/your/photo.jpg" \
  -F "size=one_inch" \
  -F "color=white"

# 获取支持的尺寸列表
curl -X GET "http://localhost:18080/idphoto/sizes"

# 获取支持的颜色列表
curl -X GET "http://localhost:18080/idphoto/colors"
```
